

using System;
using RxjhServer.Database;

namespace RxjhServer
{
    public partial class Players
    {
        public void Exchange_Coin(byte[] data, int length)
        {
            PacketModification(data, length);
            int TienVang = BitConverter.ToInt32(data, 10);
            int TienBac = BitConverter.ToInt32(data, 14);
            int TienDong = BitConverter.ToInt32(data, 18);
            if (!base.PlayerTuVong && base.NhanVat_HP > 0L && (CuaHangCaNhan == null || !CuaHangCaNhan.CuaHangCaNhanPhaiChangMoRa) && (GiaoDich == null || GiaoDich.NguoiGiaoDich == null))
            {
                Item VatPhamCLass1 = GetPackagedItems(1000001502);
                Item VatPhamCLass2 = GetPackagedItems(1000001503);
                Item VatPhamCLass3 = GetPackagedItems(1000001504);
                if (TienVang < VatPhamCLass1.GetVatPhamSoLuong && TienVang > 0)
                {
                    var amount = VatPhamCLass1.GetVatPhamSoLuong - TienVang;
                    VatPhamCLass1.VatPhamSoLuong = BitConverter.GetBytes(TienVang);
                }
                if (TienBac < VatPhamCLass2.GetVatPhamSoLuong && TienBac >0)
                {
                    // Chuyen bac thanh dong
                }
                //if (VatPhamCLass1 != null)
                //{
                //    Item_In_Bag[VatPhamCLass1.VatPhamViTri].VatPham_byte = new byte[World.Item_Db_Byte_Length];
                //}
                //if (TienVang >= 1)
                //{
                //    AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1000001502), GetParcelVacancy(this), BitConverter.GetBytes(TienVang), new byte[World.VatPham_ThuocTinh_KichThuoc]);
                //}
                //if (VatPhamCLass2 != null)
                //{
                //    Item_In_Bag[VatPhamCLass2.VatPhamViTri].VatPham_byte = new byte[World.Item_Db_Byte_Length];
                //}
                //if (TienBac >= 1)
                //{
                //    AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1000001503), GetParcelVacancy(this), BitConverter.GetBytes(TienBac), new byte[World.VatPham_ThuocTinh_KichThuoc]);
                //}
                //if (VatPhamCLass3 != null)
                //{
                //    Item_In_Bag[VatPhamCLass3.VatPhamViTri].VatPham_byte = new byte[World.Item_Db_Byte_Length];
                //}
                //if (TienDong >= 1)
                //{
                //    AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1000001504), GetParcelVacancy(this), BitConverter.GetBytes(TienDong), new byte[World.VatPham_ThuocTinh_KichThuoc]);
                //}
                Init_Item_In_Bag();
            }
            else
            {
                HeThongNhacNho("Thao Tác không hộp lệ!", 20, "Hệ Thống");
            }
        }
    }
}