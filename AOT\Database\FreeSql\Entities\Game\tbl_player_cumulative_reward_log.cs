using FreeSql.DatabaseModel;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroYulgang.Database.FreeSql.Entities.Game
{
    [JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
    public partial class tbl_player_cumulative_reward_log
    {
        [JsonProperty, Column(IsPrimary = true, IsIdentity = true, InsertValueSql = "nextval('tbl_player_cumulative_reward_log_id_seq'::regclass)")]
        public int id { get; set; }

        [JsonProperty, Column(IsNullable = false)]
        public string player_name { get; set; }

        [JsonProperty]
        public int template_id { get; set; }

        [JsonProperty]
        public int milestone_number { get; set; }

        [JsonProperty]
        public DateTime received_date { get; set; } = DateTime.Now;

        [JsonProperty]
        public int cash_spent_at_time { get; set; }

        [JsonProperty, Column(InsertValueSql = "now()")]
        public DateTime created_date { get; set; }
    }
}
