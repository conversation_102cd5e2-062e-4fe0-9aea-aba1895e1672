using System;
using System.Collections.Generic;
using System.Linq;

namespace HeroYulgang.RxjhServer.Attendance
{
    /// <summary>
    /// Enum cho loại attendance
    /// </summary>
    public enum AttendanceType
    {
        Normal,         // Attendance thường
        Comeback14Day   // Attendance 14 ngày cho player trở lại
    }
    /// <summary>
    /// Model cho attendance template - lưu thông tin về một đợt điểm danh
    /// </summary>
    public class AttendanceTemplate
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public int Month { get; set; }
        public int Year { get; set; }
        public bool IsActive { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public DateTime CreatedDate { get; set; }
        public AttendanceType AttendanceType { get; set; } = AttendanceType.Normal;
        public int EligibleAfterDays { get; set; } = 0;
        public List<AttendanceReward> Rewards { get; set; } = new List<AttendanceReward>();

        /// <summary>
        /// Kiểm tra xem attendance này có đang trong thời gian hiệu lực không
        /// </summary>
        public bool IsInValidPeriod()
        {
            var now = DateTime.Now;
            return now >= StartDate && now <= EndDate;
        }

        /// <summary>
        /// Lấy reward cho ngày cụ thể
        /// </summary>
        public AttendanceReward GetRewardForDay(int dayNumber)
        {
            return Rewards.FirstOrDefault(r => r.DayNumber == dayNumber);
        }

        /// <summary>
        /// Kiểm tra xem có reward cho ngày này không
        /// </summary>
        public bool HasRewardForDay(int dayNumber)
        {
            return Rewards.Any(r => r.DayNumber == dayNumber);
        }

        /// <summary>
        /// Kiểm tra xem đây có phải attendance 14 ngày không
        /// </summary>
        public bool IsComeback14Day()
        {
            return AttendanceType == AttendanceType.Comeback14Day;
        }
    }

    /// <summary>
    /// Model cho reward của mỗi ngày
    /// </summary>
    public class AttendanceReward
    {
        public int Id { get; set; }
        public int AttendanceId { get; set; }
        public int DayNumber { get; set; }
        public int ItemId { get; set; }
        public int ItemAmount { get; set; }

        /// <summary>
        /// Kiểm tra reward có hợp lệ không
        /// </summary>
        public bool IsValid()
        {
            return DayNumber >= 1 && DayNumber <= 28 && ItemId > 0 && ItemAmount > 0;
        }
    }

    /// <summary>
    /// Model cho attendance progress của player
    /// </summary>
    public class PlayerAttendance
    {
        public int Id { get; set; }
        public string PlayerName { get; set; }
        public int AttendanceId { get; set; }
        public string ReceivedDaysString { get; set; } = "";
        public DateTime? LastReceivedDate { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime UpdatedDate { get; set; }

        /// <summary>
        /// Lấy danh sách các ngày đã nhận thưởng
        /// </summary>
        public List<int> GetReceivedDays()
        {
            if (string.IsNullOrEmpty(ReceivedDaysString))
                return new List<int>();

            try
            {
                return ReceivedDaysString
                    .Split(',', StringSplitOptions.RemoveEmptyEntries)
                    .Select(int.Parse)
                    .ToList();
            }
            catch
            {
                return new List<int>();
            }
        }

        /// <summary>
        /// Thêm ngày đã nhận thưởng
        /// </summary>
        public void AddReceivedDay(int dayNumber)
        {
            var receivedDays = GetReceivedDays();
            if (!receivedDays.Contains(dayNumber))
            {
                receivedDays.Add(dayNumber);
                receivedDays.Sort();
                ReceivedDaysString = string.Join(",", receivedDays);
                UpdatedDate = DateTime.Now;
            }
        }

        /// <summary>
        /// Kiểm tra đã nhận thưởng ngày này chưa
        /// </summary>
        public bool HasReceivedDay(int dayNumber)
        {
            return GetReceivedDays().Contains(dayNumber);
        }

        /// <summary>
        /// Kiểm tra có thể nhận thưởng ngày hôm nay không
        /// </summary>
        public bool CanReceiveTodayReward()
        {
            var today = DateTime.Now.Day;
            
            // Kiểm tra đã nhận hôm nay chưa
            if (HasReceivedDay(today))
                return false;

            // Kiểm tra có phải ngày đầu tiên hoặc đã nhận ngày hôm qua
            if (today == 1)
                return true;

            // Kiểm tra đã nhận thưởng ngày hôm qua chưa (để đảm bảo điểm danh liên tục)
            var yesterday = today - 1;
            return HasReceivedDay(yesterday);
        }

        /// <summary>
        /// Lấy số ngày đã điểm danh
        /// </summary>
        public int GetTotalReceivedDays()
        {
            return GetReceivedDays().Count;
        }

        /// <summary>
        /// Reset attendance progress
        /// </summary>
        public void Reset()
        {
            ReceivedDaysString = "";
            LastReceivedDate = null;
            UpdatedDate = DateTime.Now;
        }
    }

    /// <summary>
    /// Enum cho trạng thái reward
    /// </summary>
    public enum AttendanceRewardStatus
    {
        CannotReceive = 0,  // Chưa thể nhận (chưa đến ngày hoặc chưa điểm danh ngày trước)
        CanReceive = 1,     // Có thể nhận
        AlreadyReceived = 2 // Đã nhận rồi
    }

    /// <summary>
    /// Helper class cho attendance logic
    /// </summary>
    public static class AttendanceHelper
    {
        /// <summary>
        /// Lấy trạng thái reward cho một ngày cụ thể
        /// Logic: Chỉ có thể nhận ngày đầu tiên chưa nhận theo thứ tự
        /// </summary>
        public static AttendanceRewardStatus GetRewardStatus(int dayNumber, PlayerAttendance playerAttendance)
        {
            if (playerAttendance == null)
                return AttendanceRewardStatus.CannotReceive;

            // Đã nhận rồi
            if (playerAttendance.HasReceivedDay(dayNumber))
                return AttendanceRewardStatus.AlreadyReceived;

            var today = DateTime.Now.Day;

            // Chưa đến ngày này
            if (dayNumber > today)
                return AttendanceRewardStatus.CannotReceive;

            // Logic mới: Chỉ có thể nhận ngày đầu tiên chưa nhận
            var nextAvailableDay = GetNextAvailableDay(playerAttendance);

            // Nếu đây là ngày đầu tiên chưa nhận và đã đến ngày đó
            if (dayNumber == nextAvailableDay && dayNumber <= today)
                return AttendanceRewardStatus.CanReceive;

            // Các ngày khác không thể nhận
            return AttendanceRewardStatus.CannotReceive;
        }

        /// <summary>
        /// Kiểm tra có thể nhận thưởng không (ngày nào có thể nhận)
        /// Logic mới: Player có thể nhận reward của ngày nào đã qua mà chưa nhận
        /// Nhưng mỗi ngày chỉ được check 1 lần
        /// </summary>
        public static bool CanReceiveTodayReward(PlayerAttendance playerAttendance)
        {
            if (playerAttendance == null)
                return false;

            // Kiểm tra đã check hôm nay chưa
            var today = DateTime.Now.Date;
            if (playerAttendance.LastReceivedDate?.Date == today)
                return false; // Đã check hôm nay rồi

            // Tìm ngày đầu tiên có thể nhận (chưa nhận và đã qua)
            var currentDay = DateTime.Now.Day;
            for (int day = 1; day <= currentDay; day++)
            {
                if (!playerAttendance.HasReceivedDay(day))
                {
                    return true; // Có ít nhất 1 ngày có thể nhận
                }
            }

            return false; // Đã nhận hết tất cả ngày có thể nhận
        }

        /// <summary>
        /// Lấy ngày đầu tiên có thể nhận thưởng (chưa nhận và đã qua)
        /// </summary>
        public static int GetNextAvailableDay(PlayerAttendance playerAttendance)
        {
            if (playerAttendance == null)
                return 1;

            var currentDay = DateTime.Now.Day;
            for (int day = 1; day <= currentDay; day++)
            {
                if (!playerAttendance.HasReceivedDay(day))
                {
                    return day;
                }
            }

            return -1; // Không có ngày nào có thể nhận
        }

        /// <summary>
        /// Tạo tên attendance từ tháng và năm
        /// </summary>
        public static string GenerateAttendanceName(int month, int year)
        {
            return $"Điểm danh tháng {month}/{year}";
        }

        /// <summary>
        /// Lấy ngày bắt đầu và kết thúc của tháng
        /// </summary>
        public static (DateTime startDate, DateTime endDate) GetMonthRange(int month, int year)
        {
            var startDate = new DateTime(year, month, 1);
            var endDate = startDate.AddMonths(1).AddDays(-1);
            return (startDate, endDate);
        }
    }
}
