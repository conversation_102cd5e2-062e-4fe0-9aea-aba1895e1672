using System;
using System.Collections.Generic;
using RxjhServer.TestBot.Core;

namespace RxjhServer.TestBot.Modules.Extended
{
    /// <summary>
    /// Module quản lý trading với ng<PERSON><PERSON>i chơi khác
    /// </summary>
    public class TradingModule : BaseBotModule
    {
        public override string ModuleName => "TradingModule";
        public override int Priority => 10;
        
        protected override int UpdateInterval => 5000; // Check every 5 seconds
        
        private readonly List<string> _trustedPlayers = new List<string>();
        private readonly Dictionary<int, int> _itemPrices = new Dictionary<int, int>();
        
        protected override bool OnCanExecute()
        {
            return Config.TradingEnabled && base.OnCanExecute();
        }
        
        protected override void OnUpdate()
        {
            // Xử lý trade requests và auto trading
            HandleTradeRequests();
        }
        
        /// <summary>
        /// Xử lý trade requests
        /// </summary>
        private void HandleTradeRequests()
        {
            try
            {
                // TODO: Implement trade request handling
                // Cần tích hợp với hệ thống trade hiện có của game
                LogDebug("Checking for trade requests...");
            }
            catch (Exception ex)
            {
                LogError($"Error handling trade requests: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Thêm player vào danh sách trusted
        /// </summary>
        /// <param name="playerName">Tên player</param>
        public void AddTrustedPlayer(string playerName)
        {
            if (!string.IsNullOrEmpty(playerName) && !_trustedPlayers.Contains(playerName))
            {
                _trustedPlayers.Add(playerName);
                LogInfo($"Added trusted player: {playerName}");
            }
        }
        
        /// <summary>
        /// Xóa player khỏi danh sách trusted
        /// </summary>
        /// <param name="playerName">Tên player</param>
        public void RemoveTrustedPlayer(string playerName)
        {
            if (_trustedPlayers.Remove(playerName))
            {
                LogInfo($"Removed trusted player: {playerName}");
            }
        }
        
        /// <summary>
        /// Set giá cho item
        /// </summary>
        /// <param name="itemId">Item ID</param>
        /// <param name="price">Giá</param>
        public void SetItemPrice(int itemId, int price)
        {
            _itemPrices[itemId] = price;
            LogInfo($"Set price for item {itemId}: {price}");
        }
        
        /// <summary>
        /// Kiểm tra xem có thể trade với player không
        /// </summary>
        /// <param name="playerName">Tên player</param>
        /// <returns>True nếu có thể trade</returns>
        public bool CanTradeWith(string playerName)
        {
            return _trustedPlayers.Contains(playerName);
        }
    }
}
