using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.IO;
using System.Linq;
using System.Net;
using System.Security.Cryptography;
using System.Threading.Tasks;
using Akka.Actor;
using Akka.IO;
using HeroYulgang.Core.Networking.Network;
using HeroYulgang.Core.Networking.Protocol;
using HeroYulgang.Core.Networking.Utils;
using HeroYulgang.Database.FreeSql;
using HeroYulgang.Services;
using HeroYulgang.Utils;
using RxjhServer;
using RxjhServer.HelperTools;
using HeroYulgang.Core.Managers;
using RxjhServer.Network;
using System.Text;

namespace HeroYulgang.Core.Networking.Core
{
    /// <summary>
    /// Lưu trữ thông tin về phiên kết nối của client
    /// Sử dụng ConnectionID cho network layer, tách biệt với PlayerSessionID
    /// </summary>
    public class ClientSession
    {
        public int ConnectionId { get; }
        public IPEndPoint RemoteEndPoint { get; }
        public DateTime ConnectedTime { get; } = DateTime.Now;
        public DateTime LastActivityTime { get; private set; } = DateTime.Now;
        public bool IsAuthenticated { get; set; } = false;
        public bool HasValidatedPacket1375 { get; set; } = false;
        public string? AccountId { get; set; }
        public string? CharacterId { get; set; }
        public IActorRef Connection { get; }
        public IActorRef? ClientConnectionActor { get; set; }

        // PlayerSessionID sẽ được cấp sau khi xác thực thành công
        public int? PlayerSessionId { get; private set; } = null;
        public bool HasPlayerSession => PlayerSessionId.HasValue;

        public ClientSession(int connectionId, EndPoint remoteEndPoint, IActorRef connection)
        {
            ConnectionId = connectionId;
            RemoteEndPoint = (IPEndPoint)remoteEndPoint;
            Connection = connection;
        }

        public void UpdateActivity()
        {
            LastActivityTime = DateTime.Now;
        }

        /// <summary>
        /// Cấp PlayerSessionID sau khi xác thực thành công
        /// </summary>
        /// <returns>PlayerSessionID đã được cấp hoặc -1 nếu thất bại</returns>
        public int AllocatePlayerSessionId()
        {
            if (HasPlayerSession)
            {
                return PlayerSessionId.Value; // Đã có rồi
            }

            int playerSessionId = SessionIdManager.Instance.AllocatePlayerSessionId();
            if (playerSessionId != -1)
            {
                PlayerSessionId = playerSessionId;
                return playerSessionId;
            }

            return -1; // Thất bại
        }

        /// <summary>
        /// Giải phóng PlayerSessionID khi ngắt kết nối
        /// </summary>
        public void ReleasePlayerSessionId()
        {
            if (HasPlayerSession)
            {
                SessionIdManager.Instance.ReleaseSessionId(PlayerSessionId.Value);
                PlayerSessionId = null;
            }
        }

        // Compatibility property để không phá vỡ code cũ
        [Obsolete("Use ConnectionId for network operations, PlayerSessionId for game operations")]
        public int SessionId => ConnectionId;
    }

    /// <summary>
    /// Actor xử lý kết nối của một client cụ thể
    /// Merged từ ClientActor với PacketHandlerActor functionality
    /// </summary>
    [DynamicallyAccessedMembers(DynamicallyAccessedMemberTypes.PublicConstructors | DynamicallyAccessedMemberTypes.NonPublicConstructors)]
    public class ClientConnection : ReceiveActor
    {
        private readonly IActorRef _connection;
        private readonly ClientSession _session;
        private Players? _player;
        private bool firstPacket = true;

        // RSA handshake state per-connection
        private RSA? _rsaHandshake;
        private string clientKey;

        private readonly ActorNetState _actorNetState;
        private readonly Dictionary<PacketType, Action<IActorRef, ClientSession, Packet>> _handlers = [];
        private readonly ActorSelection _tcpManager;

        // Buffer để xử lý packet gộp
        private readonly PacketBuffer _packetBuffer = new();

        public ClientConnection(IActorRef connection, ClientSession session)
        {
            _connection = connection;
            _session = session;

            // Lấy reference đến TcpManager - sử dụng ActorSelection trực tiếp
            _tcpManager = Context.ActorSelection("/user/tcpManager");

            // Tạo ActorNetState cho kết nối này với ConnectionID
            _actorNetState = NetworkFactory.CreateActorNetState(_connection, _session.ConnectionId, _session.RemoteEndPoint);

            // Đăng ký packet handlers
            RegisterHandlers();

            // Định nghĩa các message handler
            Receive<Tcp.Received>(HandleReceived);
            Receive<Tcp.ConnectionClosed>(HandleConnectionClosed);
            Receive<SetPlayerReference>(SetPlayerReference);
        }

        private void RegisterHandlers()
        {
            _handlers[PacketType.Valid1375] = HandlePreLogin;
            _handlers[PacketType.Login] = HandleLoginSync;
            _handlers[PacketType.CreateCharacter] = HandleCreateCharacter;
            _handlers[PacketType.CharacterList] = HandleCharacterList;
            _handlers[PacketType.LogOut] = HandleLogOut;
            _handlers[PacketType.JoinWorld] = HandleJoinWorld;
            _handlers[PacketType.ServerTime] = HandleServerTime;
            _handlers[PacketType.CheckCharacterExist] = HandleCheckCharacterExist;
            _handlers[PacketType.DeleteCharacter] = HandleDeletCharacter;
            _handlers[PacketType.VerifyLogin] = HandleVerifyLogin;
            _handlers[PacketType.ChangeChannel] = HandleChangeChannel;
            _handlers[PacketType.ChangeChannel2] = HandleChangeChannel;
            _handlers[PacketType.VerifyVersion] = HandleVerifyVersion;
            _handlers[PacketType.VerifyVersion2] = HandleVerifyVersion;
            _handlers[PacketType.UpdateConfig] = HandleUpdateConfig;
            _handlers[PacketType.Ping] = HandlePing;
        }

        private void HandleUpdateConfig(IActorRef @ref, ClientSession session, Packet packet)
        {
            if (_player != null)
            {
                var dataX = new byte[packet.Data.Length - 2];
                Buffer.BlockCopy(packet.Data, 0, dataX, 0, 6);
                Buffer.BlockCopy(packet.Data, 8, dataX, 6, packet.Data.Length - 8);
                // Task.Run(() => _player?.UpdateConfigurationBeforeLogin(dataX, dataX.Length)).GetAwaiter().GetResult();
            }
        }


        private void SetPlayerReference(SetPlayerReference message)
        {
            _player = message.Player;

            // Thiết lập player context trực tiếp trong ClientConnection
            if (_player != null && _actorNetState != null)
            {
                Logger.Instance.Debug($"Player ID {_player.AccountID} (PlayerSessionID: {_player.SessionID}) đã kết nối ConnectionID {_actorNetState.ConnectionID}");
                // Cập nhật Client của Player để sử dụng ActorNetState
                NetworkFactory.UpdatePlayerClient(_player, _actorNetState);

                Logger.Instance.Debug($"Đã thiết lập player context cho người chơi {_player.CharacterName}");
            }
        }

        private void HandleCreateCharacter(IActorRef connection, ClientSession session, Packet packet)
        {
            try
            {
                var dataX = new byte[packet.Data.Length - 2];
                Buffer.BlockCopy(packet.Data, 0, dataX, 0, 6);
                Buffer.BlockCopy(packet.Data, 8, dataX, 6, packet.Data.Length - 8);
                Task.Run(() => _player?.CreateCharacter(dataX, dataX.Length)).GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi xử lý tạo nhân vật: {ex.Message}");
            }
        }

        private void HandleCharacterList(IActorRef connection, ClientSession session, Packet packet)
        {
            try
            {
                var dataX = new byte[packet.Data.Length - 2];
                Buffer.BlockCopy(packet.Data, 0, dataX, 0, 6);
                Buffer.BlockCopy(packet.Data, 8, dataX, 6, packet.Data.Length - 8);
                Task.Run(() => _player?.GetAListOfPeople(dataX, dataX.Length)).GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi xử lý danh sách nhân vật: {ex.Message}");
            }
        }
        private void HandleLogOut(IActorRef connection, ClientSession session, Packet packet)
        {
            try
            {
                var dataX = new byte[packet.Data.Length - 2];
                Buffer.BlockCopy(packet.Data, 0, dataX, 0, 6);
                Buffer.BlockCopy(packet.Data, 8, dataX, 6, packet.Data.Length - 8);
                Task.Run(() => _player?.Logout()).GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi xử lý đăng xuất: {ex.Message}");
            }
        }
        private void HandleJoinWorld(IActorRef connection, ClientSession session, Packet packet)
        {
            try
            {
                var dataX = new byte[packet.Data.Length - 2];
                Buffer.BlockCopy(packet.Data, 0, dataX, 0, 6);
                Buffer.BlockCopy(packet.Data, 8, dataX, 6, packet.Data.Length - 8);
                Task.Run(() => _player?.CharacterLogin(dataX, dataX.Length)).GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi xử lý đăng nhập: {ex.Message}");
            }
        }
        private void HandleServerTime(IActorRef connection, ClientSession session, Packet packet)
        {
            try
            {
                Task.Run(() => _player?.ServerTime()).GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi xử lý thời gian máy chủ: {ex.Message}");
            }
        }
        private void HandleCheckCharacterExist(IActorRef connection, ClientSession session, Packet packet)
        {
            try
            {
                var dataX = new byte[packet.Data.Length - 2];
                Buffer.BlockCopy(packet.Data, 0, dataX, 0, 6);
                Buffer.BlockCopy(packet.Data, 8, dataX, 6, packet.Data.Length - 8);
                Task.Run(() => _player?.KiemTraNhanVat_CoTonTaiHayKhong(dataX ,dataX.Length)).GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi xử lý kiểm tra nhân vật tồn tại: {ex.Message}");
            }
        }
        private void HandleDeletCharacter(IActorRef connection, ClientSession session, Packet packet)
        {
            try
            {
                var dataX = new byte[packet.Data.Length - 2];
                Buffer.BlockCopy(packet.Data, 0, dataX, 0, 6);
                Buffer.BlockCopy(packet.Data, 8, dataX, 6, packet.Data.Length - 8);
                Task.Run(() => _player?.XoaBoNhanVat(dataX, dataX.Length)).GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi xử lý xóa nhân vật: {ex.Message}");
            }
        }
        private void HandleVerifyLogin(IActorRef connection, ClientSession session, Packet packet)
        {
            try
            {
                var dataX = new byte[packet.Data.Length - 2];
                Buffer.BlockCopy(packet.Data, 0, dataX, 0, 6);
                Buffer.BlockCopy(packet.Data, 8, dataX, 6, packet.Data.Length - 8);
                Task.Run(() => _player?.XacMinhThongTinDangNhapID(dataX, dataX.Length)).GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi xử lý xác minh đăng nhập: {ex.Message}");
            }
        }
        private void HandleChangeChannel(IActorRef connection, ClientSession session, Packet packet)
        {
            try
            {
                var dataX = new byte[packet.Data.Length - 2];
                Buffer.BlockCopy(packet.Data, 0, dataX, 0, 6);
                Buffer.BlockCopy(packet.Data, 8, dataX, 6, packet.Data.Length - 8);
                Task.Run(() => _player?.ChangeLineVerification(dataX, dataX.Length)).GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi xử lý đổi kênh: {ex.Message}");
            }
        }
        private void HandleVerifyVersion(IActorRef connection, ClientSession session, Packet packet)
        {
            try
            {
                var dataX = new byte[packet.Data.Length - 2];
                Buffer.BlockCopy(packet.Data, 0, dataX, 0, 6);
                Buffer.BlockCopy(packet.Data, 8, dataX, 6, packet.Data.Length - 8);
                Task.Run(() => _player?.VersionVerification(dataX, dataX.Length)).GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi xử lý xác minh phiên bản: {ex.Message}");
            }
        }
        private void HandlePing(IActorRef connection, ClientSession session, Packet packet)
        {
            try
            {
                var dataX = new byte[packet.Data.Length - 2];
                Buffer.BlockCopy(packet.Data, 0, dataX, 0, 6);
                Buffer.BlockCopy(packet.Data, 8, dataX, 6, packet.Data.Length - 8);
                Task.Run(() => _player?.HeartbeatDetection(dataX, dataX.Length)).GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi xử lý ping: {ex.Message}");
            }
        }



        private void HandleReceived(Tcp.Received message)
        {
            try
            {
                _session.UpdateActivity();

                var data = message.Data.ToArray();
                //Logger.Instance.Debug($"Nhận được {data.Length} bytes từ client {_session.SessionId}");

                // Xử lý packet buffer
                _packetBuffer.AddData(data);

                // Xử lý tất cả packet hoàn chỉnh trong buffer
                while (_packetBuffer.TryGetNextPacket(out var packetData))
                {
                    var decrypted = Crypto.DecryptPacket(packetData);
                    var packet = Packet.Parse(decrypted);
                    if (packet == null)
                    {
                        Logger.Instance.Warning($"Không thể parse packet từ client {_session.ConnectionId}");
                        _connection.Tell(Tcp.Close.Instance);
                        return;
                    }


                    if (_player!= null && _player.IsJoinWorld)
                    {
                        Logger.Instance.Debug($"[{packet.Type}]-[{_player.AccountID}] từ connection {_session.ConnectionId}");
                        var oldPacket = new byte[decrypted.Length - 2];
                        Buffer.BlockCopy(decrypted, 0, oldPacket, 0, 6);
                        Buffer.BlockCopy(decrypted, 8, oldPacket, 6, decrypted.Length - 8);
                        _player.ManagePacket(oldPacket, oldPacket.Length);
                    }
                    else{
                        Logger.Instance.Debug($"[{packet.Type}]-[Login] từ connection {_session.ConnectionId}");
                        if (_handlers.TryGetValue(packet.Type, out var handler))
                        {
                            handler(_connection, _session, packet);
                        }
                        else
                        {
                            Logger.Instance.Warning($"Không tìm thấy handler cho packet type {packet.Type} từ connection {_session.ConnectionId}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi xử lý dữ liệu nhận được từ client {_session.ConnectionId}: {ex.StackTrace}");
            }
        }

        private void HandleConnectionClosed(Tcp.ConnectionClosed message)
        {
            Logger.Instance.Info($"Kết nối từ client {_session.ConnectionId} đã bị đóng: {message.GetType().Name}");

            // Cleanup sẽ được thực hiện bởi TcpManagerActor
            // Chỉ dừng actor, không thực hiện cleanup ở đây để tránh duplicate
            Context.Stop(Self);
        }

        private void HandlePreLogin(IActorRef connection, ClientSession session, Packet packet)
        {
            // Implementation for pre-login handling
            Handle1375(packet.Data, packet.Data.Length);
        }

        /// <summary>
        /// Xử lý gói tin đăng nhập (sync version)
        /// </summary>
        private void HandleLoginSync(IActorRef connection, ClientSession session, Packet packet)
        {
            try
            {

                {
                     _session.HasValidatedPacket1375 = true;
                    var reader = new BinaryReader(new MemoryStream(packet.Data));
                    reader.BaseStream.Seek(12, SeekOrigin.Begin);
                    byte[] userNameByte = reader.ReadBytes(14);
                    string username = Encoding.Default.GetString(userNameByte).Trim().Replace("\0", string.Empty);
                    if (username.Length == 0)
                    {
                        connection.Tell(Tcp.Close.Instance);
                        return;
                    }

                    var account = AccountDb.FindAccount(username).Result;
                    if (account == null)
                    {
                        connection.Tell(Tcp.Close.Instance);
                        return;
                    }
                    // Logger.Instance.Info($"Yêu cầu đăng nhập từ {username}");

                    // Kiểm tra session đã validate packet 1375 chưa
                    if (!session.HasValidatedPacket1375)
                    {
                        Logger.Instance.Warning($"Client {username} chưa validate packet 1375 - ngắt kết nối");
                        connection.Tell(Tcp.Close.Instance);
                        return;
                    }

                    // Kiểm tra xem player đã đăng nhập chưa
                    var existingPlayer = World.allConnectedChars.Values.FirstOrDefault(p => p.AccountID == username);
                    if (existingPlayer != null)
                    {
                        Logger.Instance.Debug($"Account {existingPlayer.AccountID} đã có trong World với SessionID {existingPlayer.SessionID}");
                        try
                        {
                            existingPlayer.Client?.Dispose();
                            World.allConnectedChars.TryRemove(existingPlayer.SessionID, out _);
                            Logger.Instance.Debug($"Đã ngắt kết nối cũ cho player {username}");
                            // disconnect current connection too
                            connection.Tell(Tcp.Close.Instance);
                            return;
                        }
                        catch (Exception ex)
                        {
                            Logger.Instance.Error($"Lỗi khi ngắt kết nối cũ: {ex.Message}");
                        }
                    }

                    // XÁC THỰC VỚI LOGINSERVER TRƯỚC KHI TẠO PLAYER
                    Logger.Instance.Debug($"Bắt đầu xác thực với LoginServer cho {username}");

                    // Chuẩn bị thông tin xác thực
                    // Lấy địa chỉ ip/ port của connection hiện tại
                    var clientString = session.RemoteEndPoint.Address.ToString();
                    var clientPort = session.RemoteEndPoint.Port;
                    // var clientString = connection.ToString();
                    // var clientPort = 0; // TODO: Lấy port thực tế từ connection
                    var lanIp = username; // Tạm thời sử dụng username
                    // Logger.Instance.Debug($"Client {username} đang đăng nhập từ {clientString}:{clientPort}");

                    // Chuẩn bị data cho đăng nhập
                    var dataX = new byte[packet.Data.Length - 2];
                    Buffer.BlockCopy(packet.Data, 0, dataX, 0, 6);
                    Buffer.BlockCopy(packet.Data, 8, dataX, 6, packet.Data.Length - 8);

                    // Capture Self để tránh lỗi async context
                    var selfRef = Self;

                    // Gọi ValidateAccountLoginAsync
                    Task.Run(async () =>
                    {
                        try
                        {
                            // Validate account hiện tại với client, tiềm ẩn lỗi bảo mật
                            // Hiện tại chỉ có thể lấy ip của người chơi từ login, và kiểm tra ip
                            var res = await World.Instance.loginServerClient.ValidateAccountLoginAsync(
                                username,
                                "",
                                clientString,
                                clientPort,
                                lanIp,
                                clientPort
                            );

                            if (res.IsValid)
                            {
                                Logger.Instance.Debug($"Xác thực thành công cho {username} - tạo Player");

                                // Cấp PlayerSessionID SAU KHI xác thực thành công
                                int playerSessionId = session.AllocatePlayerSessionId();
                                if (playerSessionId == -1)
                                {
                                    Logger.Instance.Error($"Không thể cấp PlayerSessionID cho {username}");
                                    connection.Tell(Tcp.Close.Instance);
                                    return;
                                }

                                // Cập nhật PlayerSessionID trong ActorNetState
                                if (_actorNetState != null)
                                {
                                    _actorNetState.PlayerSessionID = playerSessionId;
                                    Logger.Instance.Debug($"Đã cập nhật PlayerSessionID {playerSessionId} vào ActorNetState (ConnectionID: {_actorNetState.ConnectionID})");
                                }

                                // Tạo player SAU KHI xác thực thành công
                                var player = new Players
                                {
                                    AccountID = username,
                                    LanIp = lanIp,
                                    SessionID = playerSessionId // Sử dụng PlayerSessionID
                                };

                                session.IsAuthenticated = true;
                                session.AccountId = username;

                                Logger.Instance.Debug($"Setup Player account Akka {player.AccountID} với PlayerSessionID {playerSessionId}");
                                World.allConnectedChars.TryAdd(player.SessionID, player);

                                // Thêm mapping từ ConnectionID đến PlayerSessionID
                                World.AddConnectionMapping(session.ConnectionId, playerSessionId);

                                // Thiết lập player reference trực tiếp
                                _player = player;

                                // Thiết lập player context
                                if (_player != null && _actorNetState != null)
                                {
                                    try
                                    {
                                        // Kiểm tra đồng bộ PlayerSessionID
                                        if (_player.SessionID != _actorNetState.PlayerSessionID)
                                        {
                                            Logger.Instance.Warning($"PlayerSessionID mismatch: Player={_player.SessionID}, ActorNetState={_actorNetState.PlayerSessionID}");
                                        }
                                        else
                                        {
                                            Logger.Instance.Debug($"PlayerSessionID đồng bộ thành công: {_player.SessionID}");
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        Logger.Instance.Warning($"Không thể đồng bộ PlayerSessionID: {ex.Message}");
                                    }

                                    // Cập nhật Client của Player để sử dụng ActorNetState
                                    NetworkFactory.UpdatePlayerClient(_player, _actorNetState);
                                    // Logger.Instance.Debug($"Đã thiết lập player context cho người chơi {_player.AccountID}");

                                    // Xử lý đăng nhập trực tiếp với logic từ KetNoi_DangNhap
                                    try
                                    {
                                        // Parse packet data như trong KetNoi_DangNhap
                                        var packetReader = new PacketReader(dataX, dataX.Length, false);
                                        packetReader.Seek(10, SeekOrigin.Begin);
                                        var accountName = packetReader.ReadString(29).Trim();

                                        if (accountName.Length > 16)
                                        {
                                            Logger.Instance.Error($"Account name too long: {accountName}");
                                            connection.Tell(Tcp.Close.Instance);
                                            return;
                                        }

                                        // Parse client info
                                        packetReader.Seek(74, SeekOrigin.Begin);
                                        int clientVer = packetReader.ReadInt16();
                                        packetReader.Seek(78, SeekOrigin.Begin);
                                        var lanIp = packetReader.ReadString(28).Replace("\0", string.Empty).Trim();
                                        packetReader.Seek(94, SeekOrigin.Begin);
                                        var macAddress = packetReader.ReadString(16).Trim();

                                        // Cập nhật player info
                                        _player.AccountID = accountName;
                                        _player.LanIp = lanIp;
                                        _player.MacAddress = macAddress;
                                        // LoginTime sẽ được set trong KetNoi_DangNhap2

                                        var clientString = _player.Client?.ToString() ?? "";
                                        var clientPort = _player.Client?.GetPort() ?? 0;

                                        Logger.Instance.Debug($"Acc: {accountName}, clientVer: {clientVer} lanip: {lanIp} mac: {macAddress}");

                                        // Gọi KetNoi_DangNhap2 trực tiếp
                                        try
                                        {
                                            await _player.KetNoi_DangNhap2(accountName, "NOT_ONLINE", res.LoginIp, res.LoginPort, "", "", "", "");
                                            Logger.Instance.Debug($"Người dùng {username} đã hoàn thành đăng nhập thành công");
                                            session.IsAuthenticated = true;
                                        }
                                        catch (Exception ex)
                                        {
                                            Logger.Instance.Error($"Lỗi trong KetNoi_DangNhap2 cho {username}: {ex.Message}");
                                            connection.Tell(Tcp.Close.Instance);
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        Logger.Instance.Error($"Lỗi khi xử lý đăng nhập cho {username}: {ex.Message}");
                                        connection.Tell(Tcp.Close.Instance);
                                    }
                                }
                                else
                                {
                                    Logger.Instance.Error($"_player hoặc _actorNetState là null cho {username}");
                                    connection.Tell(Tcp.Close.Instance);
                                }
                            }
                            else
                            {
                                Logger.Instance.Warning($"Xác thực thất bại cho {username}: {res.ErrorMessage}");
                                connection.Tell(Tcp.Close.Instance);
                            }
                        }
                        catch (Exception ex)
                        {
                            Logger.Instance.Error($"Lỗi xác thực với LoginServer cho {username}: {ex.Message}");
                            connection.Tell(Tcp.Close.Instance);
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi xử lý gói tin đăng nhập: {ex.Message}");

                // Gửi phản hồi thất bại - không sử dụng async
                try
                {
                    byte[] responseData = BitConverter.GetBytes(0); // 0 = thất bại
                    var response = new Packet(PacketType.Login, responseData);
                    SendPacketSync(connection, response.ToByteArray());
                }
                catch (Exception sendEx)
                {
                    Logger.Instance.Error($"Lỗi khi gửi phản hồi thất bại: {sendEx.Message}");
                }
            }
        }

        private void SendPacketSync(IActorRef connection, byte[] data)
        {
            try
            {
                // Gửi gói tin đến TcpManagerActor sử dụng reference đã lưu
                _tcpManager.Tell(new SendPacket(connection, data));

                // Ghi log gói tin gửi đi với ConnectionID
                if (data.Length >= 2)
                {
                    PacketLogger.LogOutgoingPacket(_session.ConnectionId, data);
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi gửi packet: {ex.Message}");
            }
        }

        protected override void PostStop()
        {
            Logger.Instance.Debug($"ClientConnection cho ConnectionID {_session.ConnectionId} đã dừng");

            // Cleanup sẽ được thực hiện bởi TcpManagerActor.CleanupSession()
            // Không cần duplicate cleanup ở đây để tránh race condition

            // Chỉ dispose ActorNetState nếu chưa được dispose
            try
            {
                _actorNetState?.Dispose();
            }
            catch (Exception ex)
            {
                Logger.Instance.Warning($"Lỗi khi dispose ActorNetState cho ConnectionID {_session.ConnectionId}: {ex.Message}");
            }

            base.PostStop();
        }
        private void Handle1375(byte[] data, int length)
        {
            try
            {
                // Validation packet 1375
                if (!ValidatePacket1375(data, length))
                {
                    Logger.Instance.Warning($"Packet 1375 không hợp lệ từ ConnectionID {_session.ConnectionId} - ngắt kết nối");
                    _connection.Tell(Tcp.Close.Instance);
                    return;
                }

                // Đánh dấu đã validate packet 1375
                _session.HasValidatedPacket1375 = true;
                Logger.Instance.Info($"ConnectionID {_session.ConnectionId} đã validate packet 1375 thành công");

                // Gửi response packet 1375 với ConnectionID
                var array2 = Converter.HexStringToByte("aa550000d5040000600527010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000200010055aa");
                Buffer.BlockCopy(BitConverter.GetBytes(_session.ConnectionId), 0, array2, 4, 2);
                _actorNetState.Send(array2, array2.Length);
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Handle1375 Error: {ex.Message}");
                _connection.Tell(Tcp.Close.Instance);
            }
        }

        /// <summary>
        /// Validate packet 1375 format
        /// </summary>
        private bool ValidatePacket1375(byte[] data, int length)
        {
            try
            {
                // Kiểm tra độ dài tối thiểu
                if (length < 8)
                {
                    Logger.Instance.Warning($"Packet 1375 quá ngắn: {length} bytes");
                    return false;
                }

                // Kiểm tra header aa55
                if (data[0] != 0xaa || data[1] != 0x55)
                {
                    Logger.Instance.Warning($"Packet 1375 thiếu header aa55: {data[0]:X2}{data[1]:X2}");
                    return false;
                }

                // Kiểm tra footer 55aa (2 bytes cuối)
                if (data[length - 2] != 0x55 || data[length - 1] != 0xaa)
                {
                    Logger.Instance.Warning($"Packet 1375 thiếu footer 55aa: {data[length - 2]:X2}{data[length - 1]:X2}");
                    return false;
                }

                // Kiểm tra opcode 1375 (0x0557) tại offset 6-7
                if (length >= 8)
                {
                    ushort opcode = BitConverter.ToUInt16(data, 8);
                    if (opcode != 1375)
                    {
                        Logger.Instance.Warning($"Packet opcode không phải 1375: {opcode}");
                        return false;
                    }
                }

                Logger.Instance.Debug($"Packet 1375 hợp lệ từ ConnectionID {_session.ConnectionId}");
                return true;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi validate packet 1375: {ex.Message}");
                return false;
            }
        }

    }
}
