<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hero Section with Animated Logo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            overflow-x: hidden;
        }

        .hero-section {
            position: relative;
            height: 100vh;
            background: #1a1a1a;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        /* Red overlay animation */
        .red-overlay {
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, #d61c2e, #c41828);
            animation: slideRedOverlay 2s ease-out forwards;
            z-index: 1;
        }

        @keyframes slideRedOverlay {
            0% {
                left: -100%;
            }
            100% {
                left: 0;
            }
        }

        /* Logo background */
        .logo-background {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 60vw;
            height: auto;
            opacity: 0;
            filter: brightness(0.4) drop-shadow(0 0 50px rgba(214, 28, 46, 0.3));
            animation: logoAppear 1.5s ease-out 2.2s forwards;
            z-index: 2;
        }

        @keyframes logoAppear {
            0% {
                opacity: 0;
                transform: translate(-50%, -50%) scale(0.8);
                left: -30%;
            }
            100% {
                opacity: 0.15;
                transform: translate(-50%, -50%) scale(1);
                left: 50%;
            }
        }

        /* Content */
        .hero-content {
            position: relative;
            z-index: 3;
            text-align: center;
            color: white;
            opacity: 0;
            animation: contentFadeIn 1s ease-out 3.5s forwards;
            max-width: 800px;
            padding: 0 20px;
        }

        @keyframes contentFadeIn {
            0% {
                opacity: 0;
                transform: translateY(30px);
            }
            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .hero-content h1 {
            font-size: 4rem;
            font-weight: bold;
            margin-bottom: 1.5rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            line-height: 1.2;
        }

        .hero-content p {
            font-size: 1.5rem;
            margin-bottom: 2rem;
            opacity: 0.9;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
            line-height: 1.4;
        }

        .cta-button {
            display: inline-block;
            padding: 15px 40px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            text-decoration: none;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50px;
            font-size: 1.2rem;
            font-weight: 600;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .cta-button:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.6);
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        /* Particle effects */
        .particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
            opacity: 0;
            animation: particlesFadeIn 1s ease-out 4s forwards;
        }

        @keyframes particlesFadeIn {
            to { opacity: 0.3; }
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 50%;
            animation: float 6s infinite ease-in-out;
        }

        .particle:nth-child(1) { top: 20%; left: 10%; animation-delay: 0s; }
        .particle:nth-child(2) { top: 60%; left: 85%; animation-delay: 1s; }
        .particle:nth-child(3) { top: 40%; left: 20%; animation-delay: 2s; }
        .particle:nth-child(4) { top: 80%; left: 60%; animation-delay: 3s; }
        .particle:nth-child(5) { top: 15%; left: 70%; animation-delay: 4s; }
        .particle:nth-child(6) { top: 90%; left: 30%; animation-delay: 2.5s; }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .logo-background {
                width: 80vw;
            }
            
            .hero-content h1 {
                font-size: 2.5rem;
            }
            
            .hero-content p {
                font-size: 1.2rem;
            }
            
            .cta-button {
                padding: 12px 30px;
                font-size: 1rem;
            }
        }

        @media (max-width: 480px) {
            .hero-content h1 {
                font-size: 2rem;
            }
            
            .hero-content p {
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <section class="hero-section">
        <!-- Red overlay animation -->
        <div class="red-overlay"></div>
        
        <!-- Logo background -->
        <svg class="logo-background" viewBox="0 0 755.90533 415.74798" xmlns="http://www.w3.org/2000/svg">
            <g transform="translate(-4655.4323)">
                <path d="m 0,0 h 69.238 v -192.361 h 84.635 V 0 h 27.751 l 24.41,76.93 H 0 Z"
                      fill="rgba(214, 28, 46, 0.3)"
                      transform="matrix(1.3333333,0,0,-1.3333333,4674.3293,130.91933)" />
                <path d="m 0,0 h 24.412 v -192.485 h 84.634 V 76.681 H 24.412 Z"
                      fill="rgba(214, 28, 46, 0.3)"
                      transform="matrix(1.3333333,0,0,-1.3333333,4949.5064,130.754)" />
                <path d="m 0,0 h -125.109 v 76.936 h 90.492 c 9.041,0 12.976,11.43 5.852,16.997 l -53.228,41.588 c -56.067,43.807 -25.089,133.767 46.062,133.767 H 67.243 V 192.352 H -1.313 c -9.041,0 -12.978,-11.431 -5.853,-16.998 L 46.063,133.766 C 102.128,89.959 71.151,0 0,0"
                      fill="rgba(214, 28, 46, 0.3)"
                      transform="matrix(1.3333333,0,0,-1.3333333,5292.492,387.39853)" />
            </g>
        </svg>
        
        <!-- Floating particles -->
        <div class="particles">
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
        </div>
        
        <!-- Hero content -->
        <div class="hero-content">
            <h1>Welcome to Our Brand</h1>
            <p>Experience innovation and excellence in every detail. We create extraordinary solutions that make a difference.</p>
            <a href="#" class="cta-button">Discover More</a>
        </div>
    </section>
</body>
</html>