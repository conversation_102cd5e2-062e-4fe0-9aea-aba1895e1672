using System;
using System.Collections.Generic;
using System.Linq;
using RxjhServer.TestBot.Core;

namespace RxjhServer.TestBot.Modules.Extended
{
    /// <summary>
    /// Module quản lý party system
    /// </summary>
    public class PartyModule : BaseBotModule
    {
        public override string ModuleName => "PartyModule";
        public override int Priority => 11;
        
        protected override int UpdateInterval => 10000; // Check every 10 seconds
        
        private readonly List<string> _autoAcceptPlayers = new List<string>();
        private readonly List<string> _autoInvitePlayers = new List<string>();
        
        protected override bool OnCanExecute()
        {
            return Config.PartyEnabled && base.OnCanExecute();
        }
        
        protected override void OnUpdate()
        {
            // Xử lý party logic
            HandlePartyManagement();
        }
        
        /// <summary>
        /// Xử lý party management
        /// </summary>
        private void HandlePartyManagement()
        {
            try
            {
                // Auto invite players
                HandleAutoInvites();
                
                // Check party status
                CheckPartyStatus();
            }
            catch (Exception ex)
            {
                LogError($"Error in party management: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Xử lý auto invite
        /// </summary>
        private void HandleAutoInvites()
        {
            try
            {
                if (_autoInvitePlayers.Count == 0)
                    return;
                    
                // Kiểm tra xem có đang trong party không
                if (IsInParty())
                    return;
                    
                // Tìm player để invite
                foreach (var playerName in _autoInvitePlayers)
                {
                    var targetPlayer = FindPlayerByName(playerName);
                    if (targetPlayer != null && !IsPlayerInParty(targetPlayer))
                    {
                        InviteToParty(targetPlayer);
                        break; // Chỉ invite 1 player mỗi lần
                    }
                }
            }
            catch (Exception ex)
            {
                LogError($"Error in auto invites: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Kiểm tra party status
        /// </summary>
        private void CheckPartyStatus()
        {
            try
            {
                if (IsInParty())
                {
                    var partyInfo = GetPartyInfo();
                    LogDebug($"Party status: {partyInfo}");
                }
            }
            catch (Exception ex)
            {
                LogError($"Error checking party status: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Kiểm tra xem có đang trong party không
        /// </summary>
        /// <returns>True nếu đang trong party</returns>
        public bool IsInParty()
        {
            try
            {
                return Player.TeamID != 0;
            }
            catch
            {
                return false;
            }
        }
        
        /// <summary>
        /// Tìm player theo tên
        /// </summary>
        /// <param name="playerName">Tên player</param>
        /// <returns>Player instance hoặc null</returns>
        private Players FindPlayerByName(string playerName)
        {
            try
            {
                return World.allConnectedChars.Values
                    .FirstOrDefault(p => p.CharacterName.Equals(playerName, StringComparison.OrdinalIgnoreCase));
            }
            catch
            {
                return null;
            }
        }
        
        /// <summary>
        /// Kiểm tra xem player có đang trong party không
        /// </summary>
        /// <param name="player">Player</param>
        /// <returns>True nếu đang trong party</returns>
        private bool IsPlayerInParty(Players player)
        {
            try
            {
                return player.TeamID != 0;
            }
            catch
            {
                return false;
            }
        }
        
        /// <summary>
        /// Invite player vào party
        /// </summary>
        /// <param name="targetPlayer">Player để invite</param>
        private void InviteToParty(Players targetPlayer)
        {
            try
            {
                // TODO: Implement party invite logic
                // Cần tích hợp với hệ thống party hiện có
                LogInfo($"Inviting {targetPlayer.CharacterName} to party");
            }
            catch (Exception ex)
            {
                LogError($"Error inviting player to party: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Lấy thông tin party
        /// </summary>
        /// <returns>Party info string</returns>
        public string GetPartyInfo()
        {
            try
            {
                if (!IsInParty())
                    return "Not in party";
                    
                var party = World.WToDoi[Player.TeamID];
                var memberCount = party.PlayerList.Count;
                var leaderName = party.CaptainName;
                
                return $"Party: {memberCount} members, Leader: {leaderName}";
            }
            catch
            {
                return "Party info: Unknown";
            }
        }
        
        /// <summary>
        /// Thêm player vào auto accept list
        /// </summary>
        /// <param name="playerName">Tên player</param>
        public void AddAutoAcceptPlayer(string playerName)
        {
            if (!string.IsNullOrEmpty(playerName) && !_autoAcceptPlayers.Contains(playerName))
            {
                _autoAcceptPlayers.Add(playerName);
                LogInfo($"Added auto accept player: {playerName}");
            }
        }
        
        /// <summary>
        /// Xóa player khỏi auto accept list
        /// </summary>
        /// <param name="playerName">Tên player</param>
        public void RemoveAutoAcceptPlayer(string playerName)
        {
            if (_autoAcceptPlayers.Remove(playerName))
            {
                LogInfo($"Removed auto accept player: {playerName}");
            }
        }
        
        /// <summary>
        /// Thêm player vào auto invite list
        /// </summary>
        /// <param name="playerName">Tên player</param>
        public void AddAutoInvitePlayer(string playerName)
        {
            if (!string.IsNullOrEmpty(playerName) && !_autoInvitePlayers.Contains(playerName))
            {
                _autoInvitePlayers.Add(playerName);
                LogInfo($"Added auto invite player: {playerName}");
            }
        }
        
        /// <summary>
        /// Xóa player khỏi auto invite list
        /// </summary>
        /// <param name="playerName">Tên player</param>
        public void RemoveAutoInvitePlayer(string playerName)
        {
            if (_autoInvitePlayers.Remove(playerName))
            {
                LogInfo($"Removed auto invite player: {playerName}");
            }
        }
        
        /// <summary>
        /// Leave party hiện tại
        /// </summary>
        public void LeaveParty()
        {
            try
            {
                if (IsInParty())
                {
                    // TODO: Implement leave party logic
                    LogInfo("Left party");
                }
            }
            catch (Exception ex)
            {
                LogError($"Error leaving party: {ex.Message}");
            }
        }
    }
}
