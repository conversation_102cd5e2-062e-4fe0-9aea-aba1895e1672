using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Threading;
using HeroYulgang.Helpers;

namespace HeroYulgang.Core.Managers
{
    /// <summary>
    /// Quản lý ConnectionID cho network sessions (Akka ClientSession, NetCore GameSession)
    /// Không có giới hạn về số lượng connection vì chỉ dùng cho network layer
    /// Thread-safe, ConnectionID luôn tăng dần không tái sử dụng để tránh xung đột
    /// </summary>
    public sealed class ConnectionIdManager
    {
        private static readonly Lazy<ConnectionIdManager> _instance = new(() => new ConnectionIdManager());
        public static ConnectionIdManager Instance => _instance.Value;

        // Counter cho ConnectionID - bắt đầu từ 1
        private int _connectionIdCounter = 0;
        
        // Lưu trữ các ConnectionID đã được cấp phát để tracking
        private readonly ConcurrentDictionary<int, DateTime> _allocatedConnections = new();

        private ConnectionIdManager()
        {
            LogHelper.WriteLine(LogLevel.Debug, "ConnectionIdManager initialized - ConnectionID always increments, no reuse");
        }

        /// <summary>
        /// Cấp phát ConnectionID mới cho network session
        /// ConnectionID luôn tăng dần, không tái sử dụng để tránh xung đột tên actor
        /// </summary>
        /// <returns>ConnectionID duy nhất</returns>
        public int AllocateConnectionId()
        {
            // Luôn tạo ConnectionID mới, không tái sử dụng
            int connectionId = Interlocked.Increment(ref _connectionIdCounter);
            _allocatedConnections[connectionId] = DateTime.Now;

            LogHelper.WriteLine(LogLevel.Debug, $"Allocated new ConnectionID: {connectionId}");
            return connectionId;
        }

        /// <summary>
        /// Giải phóng ConnectionID khỏi tracking (không tái sử dụng)
        /// </summary>
        /// <param name="connectionId">ConnectionID cần giải phóng</param>
        /// <returns>true nếu giải phóng thành công</returns>
        public bool ReleaseConnectionId(int connectionId)
        {
            if (_allocatedConnections.TryRemove(connectionId, out DateTime allocatedTime))
            {
                // Không enqueue vào _releasedConnectionIds vì không tái sử dụng
                var duration = DateTime.Now - allocatedTime;
                LogHelper.WriteLine(LogLevel.Debug, $"Released ConnectionID: {connectionId} (duration: {duration.TotalSeconds:F1}s)");
                return true;
            }

            LogHelper.WriteLine(LogLevel.Warning, $"Attempted to release unallocated ConnectionID: {connectionId}");
            return false;
        }

        /// <summary>
        /// Kiểm tra ConnectionID có đang được sử dụng không
        /// </summary>
        /// <param name="connectionId">ConnectionID cần kiểm tra</param>
        /// <returns>true nếu đang được sử dụng</returns>
        public bool IsConnectionIdAllocated(int connectionId)
        {
            return _allocatedConnections.ContainsKey(connectionId);
        }

        /// <summary>
        /// Lấy số lượng ConnectionID đang được sử dụng
        /// </summary>
        /// <returns>Số lượng connection đang active</returns>
        public int GetActiveConnectionCount()
        {
            return _allocatedConnections.Count;
        }

        /// <summary>
        /// Lấy tổng số ConnectionID đã được cấp phát (luôn tăng dần)
        /// </summary>
        /// <returns>Tổng số ConnectionID đã cấp phát</returns>
        public int GetTotalAllocatedCount()
        {
            return _connectionIdCounter;
        }

        /// <summary>
        /// Lấy thống kê về ConnectionID
        /// </summary>
        /// <returns>Thông tin thống kê</returns>
        public ConnectionIdStats GetStats()
        {
            return new ConnectionIdStats
            {
                ActiveConnections = GetActiveConnectionCount(),
                ReusableConnections = 0, // Không tái sử dụng
                TotalAllocated = _connectionIdCounter,
                OldestConnectionTime = GetOldestConnectionTime()
            };
        }

        /// <summary>
        /// Lấy thời gian của connection cũ nhất
        /// </summary>
        /// <returns>Thời gian của connection cũ nhất hoặc null nếu không có connection nào</returns>
        private DateTime? GetOldestConnectionTime()
        {
            DateTime? oldest = null;
            foreach (var time in _allocatedConnections.Values)
            {
                if (oldest == null || time < oldest)
                {
                    oldest = time;
                }
            }
            return oldest;
        }

        /// <summary>
        /// Dọn dẹp các ConnectionID cũ (nếu cần thiết)
        /// Thường không cần vì ConnectionID sẽ được release khi connection đóng
        /// </summary>
        /// <param name="maxAge">Thời gian tối đa một connection có thể tồn tại</param>
        /// <returns>Số lượng ConnectionID đã được dọn dẹp</returns>
        public int CleanupOldConnections(TimeSpan maxAge)
        {
            var cutoffTime = DateTime.Now - maxAge;
            var toRemove = new List<int>();
            
            foreach (var kvp in _allocatedConnections)
            {
                if (kvp.Value < cutoffTime)
                {
                    toRemove.Add(kvp.Key);
                }
            }
            
            int cleanedCount = 0;
            foreach (var connectionId in toRemove)
            {
                if (ReleaseConnectionId(connectionId))
                {
                    cleanedCount++;
                    LogHelper.WriteLine(LogLevel.Warning, $"Force released old ConnectionID: {connectionId}");
                }
            }
            
            return cleanedCount;
        }
    }

    /// <summary>
    /// Thống kê về ConnectionID
    /// </summary>
    public class ConnectionIdStats
    {
        public int ActiveConnections { get; set; }
        public int ReusableConnections { get; set; }
        public int TotalAllocated { get; set; }
        public DateTime? OldestConnectionTime { get; set; }
        
        public override string ToString()
        {
            var oldestAge = OldestConnectionTime.HasValue 
                ? (DateTime.Now - OldestConnectionTime.Value).TotalMinutes.ToString("F1") + " minutes"
                : "N/A";
                
            return $"Active: {ActiveConnections}, Reusable: {ReusableConnections}, Total: {TotalAllocated}, Oldest: {oldestAge}";
        }
    }
}
