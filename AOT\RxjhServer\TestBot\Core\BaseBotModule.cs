using System;
using HeroYulgang.Helpers;

namespace RxjhServer.TestBot.Core
{
    /// <summary>
    /// Base class cho tất cả các bot module
    /// </summary>
    public abstract class BaseBotModule : IBotModule
    {
        protected Players Player { get; private set; }
        protected BotConfig Config { get; private set; }
        protected DateTime LastUpdate { get; private set; }
        
        private bool _disposed = false;
        
        public abstract string ModuleName { get; }
        public virtual bool IsEnabled { get; set; } = true;
        public abstract int Priority { get; }
        
        /// <summary>
        /// Thời gian tối thiểu giữa các lần update (milliseconds)
        /// </summary>
        protected virtual int UpdateInterval => 1000;
        
        /// <summary>
        /// Khởi tạo module
        /// </summary>
        /// <param name="player">Player instance</param>
        /// <param name="config">Bot configuration</param>
        public virtual void Initialize(Players player, BotConfig config)
        {
            Player = player ?? throw new ArgumentNullException(nameof(player));
            Config = config ?? throw new ArgumentNullException(nameof(config));
            LastUpdate = DateTime.Now;
            
            OnInitialize();
            LogDebug($"Module {ModuleName} initialized");
        }
        
        /// <summary>
        /// Override để thực hiện initialization logic cụ thể
        /// </summary>
        protected virtual void OnInitialize()
        {
            // Override trong derived class
        }
        
        /// <summary>
        /// Cập nhật logic module
        /// </summary>
        public void Update()
        {
            if (!CanExecute())
                return;
                
            try
            {
                // Kiểm tra thời gian update
                if ((DateTime.Now - LastUpdate).TotalMilliseconds < UpdateInterval)
                    return;
                    
                OnUpdate();
                LastUpdate = DateTime.Now;
            }
            catch (Exception ex)
            {
                LogError($"Error in {ModuleName} update: {ex.Message}");
                OnError(ex);
            }
        }
        
        /// <summary>
        /// Override để thực hiện update logic cụ thể
        /// </summary>
        protected abstract void OnUpdate();
        
        /// <summary>
        /// Kiểm tra xem module có thể chạy không
        /// </summary>
        /// <returns>True nếu có thể chạy</returns>
        public virtual bool CanExecute()
        {
            if (!IsEnabled || Player == null || Config == null)
                return false;
                
            // Kiểm tra player còn valid không
            if (Player.PlayerTuVong || Player.NhanVat_HP <= 0)
                return false;
                
            return OnCanExecute();
        }
        
        /// <summary>
        /// Override để thực hiện kiểm tra cụ thể
        /// </summary>
        /// <returns>True nếu có thể chạy</returns>
        protected virtual bool OnCanExecute()
        {
            return true;
        }
        
        /// <summary>
        /// Dọn dẹp tài nguyên
        /// </summary>
        public virtual void Cleanup()
        {
            try
            {
                OnCleanup();
                LogDebug($"Module {ModuleName} cleaned up");
            }
            catch (Exception ex)
            {
                LogError($"Error cleaning up {ModuleName}: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Override để thực hiện cleanup logic cụ thể
        /// </summary>
        protected virtual void OnCleanup()
        {
            // Override trong derived class
        }
        
        /// <summary>
        /// Xử lý lỗi
        /// </summary>
        /// <param name="ex">Exception</param>
        protected virtual void OnError(Exception ex)
        {
            // Override để xử lý lỗi cụ thể
        }
        
        /// <summary>
        /// Dispose resources
        /// </summary>
        public void Dispose()
        {
            if (_disposed)
                return;
                
            try
            {
                Cleanup();
                OnDispose();
                _disposed = true;
            }
            catch (Exception ex)
            {
                LogError($"Error disposing {ModuleName}: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Override để thực hiện dispose logic cụ thể
        /// </summary>
        protected virtual void OnDispose()
        {
            // Override trong derived class
        }
        
        #region Helper Methods
        
        /// <summary>
        /// Lấy setting tùy chỉnh cho module
        /// </summary>
        /// <typeparam name="T">Kiểu dữ liệu</typeparam>
        /// <param name="settingName">Tên setting</param>
        /// <param name="defaultValue">Giá trị mặc định</param>
        /// <returns>Giá trị setting</returns>
        protected T GetSetting<T>(string settingName, T defaultValue = default(T))
        {
            return Config.GetModuleSetting(ModuleName, settingName, defaultValue);
        }
        
        /// <summary>
        /// Đặt setting tùy chỉnh cho module
        /// </summary>
        /// <param name="settingName">Tên setting</param>
        /// <param name="value">Giá trị</param>
        protected void SetSetting(string settingName, object value)
        {
            Config.SetModuleSetting(ModuleName, settingName, value);
        }
        
        /// <summary>
        /// Kiểm tra xem đã đủ thời gian để thực hiện action chưa
        /// </summary>
        /// <param name="lastActionTime">Thời gian action cuối</param>
        /// <param name="intervalMs">Khoảng thời gian tối thiểu (ms)</param>
        /// <returns>True nếu đã đủ thời gian</returns>
        protected bool IsTimeToAct(DateTime lastActionTime, int intervalMs)
        {
            return (DateTime.Now - lastActionTime).TotalMilliseconds >= intervalMs;
        }
        
        /// <summary>
        /// Tính khoảng cách giữa 2 điểm
        /// </summary>
        /// <param name="x1">X1</param>
        /// <param name="y1">Y1</param>
        /// <param name="x2">X2</param>
        /// <param name="y2">Y2</param>
        /// <returns>Khoảng cách</returns>
        protected float CalculateDistance(float x1, float y1, float x2, float y2)
        {
            var dx = x1 - x2;
            var dy = y1 - y2;
            return (float)Math.Sqrt(dx * dx + dy * dy);
        }
        
        /// <summary>
        /// Kiểm tra xem player có trong phạm vi không
        /// </summary>
        /// <param name="targetX">X đích</param>
        /// <param name="targetY">Y đích</param>
        /// <param name="range">Phạm vi</param>
        /// <returns>True nếu trong phạm vi</returns>
        protected bool IsInRange(float targetX, float targetY, float range)
        {
            return CalculateDistance(Player.PosX, Player.PosY, targetX, targetY) <= range;
        }
        
        #endregion
        
        #region Logging Methods
        
        protected void LogDebug(string message)
        {
            if (Config.DebugMode)
            {
                LogHelper.WriteLine(LogLevel.Debug, $"[{ModuleName}] {message}");
            }
        }
        
        protected void LogInfo(string message)
        {
            LogHelper.WriteLine(LogLevel.Info, $"[{ModuleName}] {message}");
        }
        
        protected void LogWarning(string message)
        {
            LogHelper.WriteLine(LogLevel.Warning, $"[{ModuleName}] {message}");
        }
        
        protected void LogError(string message)
        {
            LogHelper.WriteLine(LogLevel.Error, $"[{ModuleName}] {message}");
        }
        
        #endregion
    }
}
