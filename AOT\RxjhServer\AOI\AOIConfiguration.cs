using HeroYulgang.Helpers;
using System;
using System.Collections.Generic;


namespace RxjhServer.AOI
{
    /// <summary>
    /// Configuration settings for the Area of Interest (AOI) system
    /// Allows runtime configuration and tuning of AOI parameters
    /// </summary>
    public class AOIConfiguration
    {
        #region Singleton Pattern
        
        private static AOIConfiguration _instance;
        private static readonly object _lock = new object();

        /// <summary>
        /// Get the singleton instance of AOIConfiguration
        /// </summary>
        public static AOIConfiguration Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        _instance ??= new AOIConfiguration();
                    }
                }
                return _instance;
            }
        }
        
        #endregion
        
        #region Configuration Properties
        
        /// <summary>
        /// Whether the AOI system is enabled globally
        /// </summary>
        public bool IsEnabled { get; set; } = true;
        
        /// <summary>
        /// Maximum time before forcing an AOI update (in seconds)
        /// </summary>
        public int MaxUpdateInterval { get; set; } = 5;

        /// <summary>
        /// Time window to consider a grid change as "recent" for immediate updates (in milliseconds)
        /// </summary>
        public int GridChangeWindowMs { get; set; } = 500;

        /// <summary>
        /// Whether to use overlap zones for smooth grid transitions
        /// </summary>
        public bool UseOverlapZones { get; set; } = true;

        /// <summary>
        /// Whether to optimize adjacent grid selection based on position
        /// </summary>
        public bool OptimizeAdjacentGrids { get; set; } = true;
        
        /// <summary>
        /// Whether to enable debug logging for AOI operations
        /// </summary>
        public bool EnableDebugLogging { get; set; } = false;
        
        /// <summary>
        /// Maps that should not use AOI system (fallback to old system)
        /// </summary>
        public HashSet<int> DisabledMaps { get; set; } = new HashSet<int>();
        
        /// <summary>
        /// Custom AOI radius for specific maps
        /// </summary>
        public Dictionary<int, int> CustomAOIRadius { get; set; } = new Dictionary<int, int>();
        
        
        #endregion
        
        #region Constructor
        
        /// <summary>
        /// Private constructor for singleton pattern
        /// </summary>
        private AOIConfiguration()
        {
            LoadDefaultConfiguration();
            LogHelper.WriteLine(LogLevel.Info, "AOIConfiguration initialized with default settings");
        }
        
        #endregion
        
        #region Configuration Methods
        
        /// <summary>
        /// Load default configuration settings
        /// </summary>
        private void LoadDefaultConfiguration()
        {
            try
            {
                CustomAOIRadius[7101] = 1000; // Larger radius for special maps
                
                LogHelper.WriteLine(LogLevel.Info, "Default AOI configuration loaded");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error loading default AOI configuration: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Check if AOI system should be used for a specific map
        /// </summary>
        /// <param name="mapID">Map ID to check</param>
        /// <returns>True if AOI should be used for this map</returns>
        public bool ShouldUseAOI(int mapID)
        {
            return IsEnabled && !DisabledMaps.Contains(mapID);
        }
        
        #endregion
    }
}
