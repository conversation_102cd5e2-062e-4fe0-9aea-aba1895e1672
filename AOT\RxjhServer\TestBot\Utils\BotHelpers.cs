using System;
using System.Collections.Generic;
using System.Linq;
using RxjhServer.TestBot.Core;

namespace RxjhServer.TestBot.Utils
{
    /// <summary>
    /// Helper methods cho Test Bot system
    /// </summary>
    public static class BotHelpers
    {
        /// <summary>
        /// Tính khoảng cách giữa 2 điểm
        /// </summary>
        /// <param name="x1">X1</param>
        /// <param name="y1">Y1</param>
        /// <param name="x2">X2</param>
        /// <param name="y2">Y2</param>
        /// <returns>Kho<PERSON>ng cách</returns>
        public static float CalculateDistance(float x1, float y1, float x2, float y2)
        {
            var dx = x1 - x2;
            var dy = y1 - y2;
            return (float)Math.Sqrt(dx * dx + dy * dy);
        }
        
        /// <summary>
        /// Kiểm tra xem điểm có trong phạm vi không
        /// </summary>
        /// <param name="sourceX">X nguồn</param>
        /// <param name="sourceY">Y nguồn</param>
        /// <param name="targetX">X đích</param>
        /// <param name="targetY">Y đích</param>
        /// <param name="range">Phạm vi</param>
        /// <returns>True nếu trong phạm vi</returns>
        public static bool IsInRange(float sourceX, float sourceY, float targetX, float targetY, float range)
        {
            return CalculateDistance(sourceX, sourceY, targetX, targetY) <= range;
        }
        
        /// <summary>
        /// Lấy player theo tên
        /// </summary>
        /// <param name="playerName">Tên player</param>
        /// <returns>Player instance hoặc null</returns>
        public static Players GetPlayerByName(string playerName)
        {
            try
            {
                return World.allConnectedChars.Values
                    .FirstOrDefault(p => p.CharacterName.Equals(playerName, StringComparison.OrdinalIgnoreCase));
            }
            catch
            {
                return null;
            }
        }
        
        /// <summary>
        /// Lấy danh sách player online
        /// </summary>
        /// <returns>Danh sách player</returns>
        public static List<Players> GetOnlinePlayers()
        {
            try
            {
                return World.allConnectedChars.Values.ToList();
            }
            catch
            {
                return new List<Players>();
            }
        }
        
        /// <summary>
        /// Lấy danh sách player đang offline training
        /// </summary>
        /// <returns>Danh sách player</returns>
        public static List<Players> GetOfflineTrainingPlayers()
        {
            try
            {
                return World.allConnectedChars.Values
                    .Where(p => p.Offline_TreoMay_Mode_ON_OFF == 1)
                    .ToList();
            }
            catch
            {
                return new List<Players>();
            }
        }
        
        /// <summary>
        /// Lấy danh sách player đang sử dụng TestBot
        /// </summary>
        /// <returns>Danh sách player</returns>
        public static List<Players> GetTestBotPlayers()
        {
            try
            {
                var botManager = TestBotManager.Instance;
                var result = new List<Players>();
                
                foreach (var player in World.allConnectedChars.Values)
                {
                    if (botManager.HasActiveBot(player.SessionID))
                    {
                        result.Add(player);
                    }
                }
                
                return result;
            }
            catch
            {
                return new List<Players>();
            }
        }
        
        /// <summary>
        /// Validate config
        /// </summary>
        /// <param name="config">Config để validate</param>
        /// <returns>True nếu config hợp lệ</returns>
        public static bool ValidateConfig(BotConfig config)
        {
            try
            {
                if (config == null)
                    return false;
                    
                // Kiểm tra các giá trị cơ bản
                if (config.UpdateInterval < 100 || config.UpdateInterval > 60000)
                    return false;
                    
                if (config.CombatRange < 10 || config.CombatRange > 1000)
                    return false;
                    
                if (config.MaxRangeFromHome < 50 || config.MaxRangeFromHome > 2000)
                    return false;
                    
                if (config.HealThreshold < 0.1f || config.HealThreshold > 1.0f)
                    return false;
                    
                if (config.ManaThreshold < 0.1f || config.ManaThreshold > 1.0f)
                    return false;
                    
                return true;
            }
            catch
            {
                return false;
            }
        }
        
        /// <summary>
        /// Tạo config mặc định an toàn
        /// </summary>
        /// <param name="player">Player instance</param>
        /// <returns>Safe config</returns>
        public static BotConfig CreateSafeConfig(Players player)
        {
            try
            {
                var config = new BotConfig
                {
                    IsEnabled = true,
                    UpdateInterval = 1000,
                    DebugMode = false,
                    
                    // Movement
                    MovementEnabled = true,
                    HomeX = player.PosX,
                    HomeY = player.PosY,
                    HomeMapId = player.MapID,
                    MaxRangeFromHome = 150,
                    AntiAfkInterval = 3600000,
                    
                    // Combat
                    CombatEnabled = true,
                    CombatRange = 100,
                    PreferredSkillId = 0,
                    PvPEnabled = false,
                    
                    // Status
                    AutoHealEnabled = true,
                    HealThreshold = 0.7f,
                    AutoManaEnabled = true,
                    ManaThreshold = 0.2f,
                    
                    // Buff
                    AutoBuffEnabled = true,
                    BuffInterval = 60000,
                    
                    // Item
                    ItemPickupEnabled = true,
                    ItemPickupRange = 50,
                    
                    // Extended features disabled by default
                    TradingEnabled = false,
                    PartyEnabled = false,
                    NPCInteractionEnabled = false,
                    MarketplaceEnabled = false,
                    CashShopEnabled = false,
                    ChatEnabled = false
                };
                
                return config;
            }
            catch
            {
                return new BotConfig();
            }
        }
        
        /// <summary>
        /// Lấy thống kê hệ thống
        /// </summary>
        /// <returns>System stats</returns>
        public static string GetSystemStatistics()
        {
            try
            {
                var totalOnline = World.allConnectedChars.Count;
                var totalOffline = World.OffLine_SoLuong;
                var testBotCount = TestBotManager.Instance.ActiveBotCount;
                var legacyBotCount = totalOffline - testBotCount;
                
                return $"Online: {totalOnline} | Offline Total: {totalOffline} | TestBot: {testBotCount} | Legacy: {legacyBotCount}";
            }
            catch
            {
                return "Statistics: Unknown";
            }
        }
        
        /// <summary>
        /// Format thời gian
        /// </summary>
        /// <param name="timeSpan">TimeSpan</param>
        /// <returns>Formatted string</returns>
        public static string FormatTimeSpan(TimeSpan timeSpan)
        {
            try
            {
                if (timeSpan.TotalDays >= 1)
                    return $"{timeSpan.Days}d {timeSpan.Hours}h {timeSpan.Minutes}m";
                else if (timeSpan.TotalHours >= 1)
                    return $"{timeSpan.Hours}h {timeSpan.Minutes}m";
                else if (timeSpan.TotalMinutes >= 1)
                    return $"{timeSpan.Minutes}m {timeSpan.Seconds}s";
                else
                    return $"{timeSpan.Seconds}s";
            }
            catch
            {
                return "Unknown";
            }
        }
        
        /// <summary>
        /// Kiểm tra xem skill có hợp lệ cho job không
        /// </summary>
        /// <param name="skillId">Skill ID</param>
        /// <param name="jobId">Job ID</param>
        /// <returns>True nếu hợp lệ</returns>
        public static bool IsValidSkillForJob(int skillId, int jobId)
        {
            try
            {
                if (skillId <= 0 || jobId <= 0)
                    return false;
                    
                // Kiểm tra skill có tồn tại không
                if (!World.MagicList.ContainsKey(skillId))
                    return false;
                    
                var skill = World.MagicList[skillId];
                return skill.FLD_JOB == jobId || skill.FLD_JOB == 0; // 0 = all jobs
            }
            catch
            {
                return false;
            }
        }
    }
}
