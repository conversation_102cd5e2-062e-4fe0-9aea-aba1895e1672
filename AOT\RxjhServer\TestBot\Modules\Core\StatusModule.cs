using System;
using RxjhServer.TestBot.Core;

namespace RxjhServer.TestBot.Modules.Core
{
    /// <summary>
    /// Module quản lý trạng thái HP/MP và death handling
    /// </summary>
    public class StatusModule : BaseBotModule
    {
        public override string ModuleName => "StatusModule";
        public override int Priority => 1; // Highest priority
        
        protected override int UpdateInterval => 500; // Check every 500ms
        
        private DateTime _lastHealTime = DateTime.MinValue;
        private DateTime _lastManaTime = DateTime.MinValue;
        
        protected override bool OnCanExecute()
        {
            // Status module luôn chạy để kiểm tra HP/MP
            return true;
        }
        
        protected override void OnUpdate()
        {
            // Kiểm tra và xử lý death
            HandleDeath();
            
            // Kiểm tra và hồi HP
            HandleHealthRegeneration();
            
            // Kiểm tra và hồi MP
            HandleManaRegeneration();
        }
        
        /// <summary>
        /// Xử lý khi player chết
        /// </summary>
        private void HandleDeath()
        {
            if (Player.NhanVat_HP <= 0 || Player.PlayerTuVong)
            {
                try
                {
                    LogInfo($"Player {Player.CharacterName} died, respawning at home position");
                    
                    // Teleport về vị trí gốc
                    Player.Mobile(Config.HomeX, Config.HomeY, Player.PosZ, Config.HomeMapId, 0);
                    
                    // Hồi full HP
                    Player.NhanVat_HP = Player.CharacterMax_HP;
                    Player.CapNhat_HP_MP_SP();
                    Player.PlayerTuVong = false;
                    // TODO : Cho nhân vật disconnect để đảm bảo pill không ibj mất 
                    
                    LogDebug("Player respawned successfully");
                }
                catch (Exception ex)
                {
                    LogError($"Error handling death: {ex.Message}");
                }
            }
        }
        
        /// <summary>
        /// Xử lý hồi máu tự động
        /// </summary>
        private void HandleHealthRegeneration()
        {
            if (!Config.AutoHealEnabled)
                return;
                
            try
            {
                // Kiểm tra HP threshold
                var hpPercentage = (float)Player.NhanVat_HP / Player.CharacterMax_HP;
                
                if (hpPercentage <= Config.HealThreshold)
                {
                    // Kiểm tra cooldown
                    if (IsTimeToAct(_lastHealTime, 2000)) // 2 seconds cooldown
                    {
                        // Hồi máu
                        // TODO: Get máu trong thùng đồ và heal bằng máu đó
                        var healAmount = GetSetting("HealAmount", 20000);
                        Player.AddBlood(healAmount);
                        Player.EffectOfTakingMedicine(**********); // HP potion effect
                        Player.CapNhat_HP_MP_SP();
                        
                        _lastHealTime = DateTime.Now;
                        LogDebug($"Auto healed for {healAmount} HP (Current: {Player.NhanVat_HP}/{Player.CharacterMax_HP})");
                    }
                }
            }
            catch (Exception ex)
            {
                LogError($"Error in health regeneration: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Xử lý hồi mana tự động
        /// </summary>
        private void HandleManaRegeneration()
        {
            if (!Config.AutoManaEnabled)
                return;
                
            try
            {
                // Kiểm tra MP threshold
                var mpPercentage = (float)Player.NhanVat_MP / Player.CharacterMax_MP;
                
                if (mpPercentage <= Config.ManaThreshold)
                {
                    // Kiểm tra cooldown
                    if (IsTimeToAct(_lastManaTime, 2000)) // 2 seconds cooldown
                    {
                        // Hồi mana
                        // TODO: Get mana trong thùng đồ và heal bằng mana đó
                        var manaAmount = GetSetting("ManaAmount", 10000);
                        Player.MagicPlus(manaAmount);
                        Player.EffectOfTakingMedicine(1000000104); // MP potion effect
                        Player.CapNhat_HP_MP_SP();
                        
                        _lastManaTime = DateTime.Now;
                        LogDebug($"Auto restored {manaAmount} MP (Current: {Player.NhanVat_MP}/{Player.CharacterMax_MP})");
                    }
                }
            }
            catch (Exception ex)
            {
                LogError($"Error in mana regeneration: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Lấy thông tin trạng thái hiện tại
        /// </summary>
        /// <returns>Status info string</returns>
        public string GetStatusInfo()
        {
            try
            {
                var hpPercent = (float)Player.NhanVat_HP / Player.CharacterMax_HP * 100;
                var mpPercent = (float)Player.NhanVat_MP / Player.CharacterMax_MP * 100;
                
                return $"HP: {hpPercent:F1}% ({Player.NhanVat_HP}/{Player.CharacterMax_HP}) | " +
                       $"MP: {mpPercent:F1}% ({Player.NhanVat_MP}/{Player.CharacterMax_MP}) | " +
                       $"Alive: {!Player.PlayerTuVong}";
            }
            catch
            {
                return "Status: Unknown";
            }
        }
        
        /// <summary>
        /// Kiểm tra xem player có khỏe mạnh không
        /// </summary>
        /// <returns>True nếu HP/MP đủ để hoạt động</returns>
        public bool IsHealthy()
        {
            try
            {
                if (Player.PlayerTuVong || Player.NhanVat_HP <= 0)
                    return false;
                    
                var hpPercent = (float)Player.NhanVat_HP / Player.CharacterMax_HP;
                var mpPercent = (float)Player.NhanVat_MP / Player.CharacterMax_MP;
                
                // Coi là healthy nếu HP > 30% và MP > 10%
                return hpPercent > 0.3f && mpPercent > 0.1f;
            }
            catch
            {
                return false;
            }
        }
        
        /// <summary>
        /// Force heal ngay lập tức
        /// </summary>
        public void ForceHeal()
        {
            try
            {
                if (Config.AutoHealEnabled)
                {
                    var healAmount = GetSetting("HealAmount", 20000);
                    Player.AddBlood(healAmount);
                    Player.EffectOfTakingMedicine(**********);
                    Player.CapNhat_HP_MP_SP();
                    
                    _lastHealTime = DateTime.Now;
                    LogInfo($"Force healed for {healAmount} HP");
                }
            }
            catch (Exception ex)
            {
                LogError($"Error in force heal: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Force restore mana ngay lập tức
        /// </summary>
        public void ForceRestoreMana()
        {
            try
            {
                if (Config.AutoManaEnabled)
                {
                    var manaAmount = GetSetting("ManaAmount", 10000);
                    Player.MagicPlus(manaAmount);
                    Player.EffectOfTakingMedicine(1000000104);
                    Player.CapNhat_HP_MP_SP();
                    
                    _lastManaTime = DateTime.Now;
                    LogInfo($"Force restored {manaAmount} MP");
                }
            }
            catch (Exception ex)
            {
                LogError($"Error in force mana restore: {ex.Message}");
            }
        }
    }
}
