using System;
using RxjhServer.TestBot.Core;
using RxjhServer.HelperTools;

namespace RxjhServer.TestBot.Modules.Core
{
    /// <summary>
    /// Module quản lý di chuyển và anti-AFK
    /// </summary>
    public class MovementModule : BaseBotModule
    {
        public override string ModuleName => "MovementModule";
        public override int Priority => 2;
        
        protected override int UpdateInterval => 1000; // Check every second
        
        private DateTime _lastAntiAfkTime = DateTime.MinValue;
        private DateTime _lastPositionCheck = DateTime.MinValue;
        
        protected override bool OnCanExecute()
        {
            return Config.MovementEnabled && base.OnCanExecute();
        }
        
        protected override void OnUpdate()
        {
            // Kiểm tra và xử lý anti-AFK
            HandleAntiAfk();
            
            // Kiểm tra và trở về vị trí gốc nếu đi xa
            HandlePositionValidation();
        }
        
        /// <summary>
        /// Xử lý anti-AFK movement
        /// </summary>
        private void HandleAntiAfk()
        {
            try
            {
                if (IsTimeToAct(_lastAntiAfkTime, Config.AntiAfkInterval))
                {
                    // Di chuyển random để tránh AFK
                    var randomX = RNG.Next(-30, 30);
                    var randomY = RNG.Next(-30, 30);
                    var targetX = 420 + randomX; // Default AFK position
                    var targetY = 1740 + randomY;
                    
                    LogDebug($"Anti-AFK movement to ({targetX}, {targetY})");
                    Player.Mobile(targetX, targetY, Player.PosZ, 101, 0);
                    
                    _lastAntiAfkTime = DateTime.Now;
                }
            }
            catch (Exception ex)
            {
                LogError($"Error in anti-AFK movement: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Kiểm tra và trở về vị trí gốc nếu đi xa
        /// </summary>
        private void HandlePositionValidation()
        {
            try
            {
                if (!IsTimeToAct(_lastPositionCheck, 5000)) // Check every 5 seconds
                    return;
                    
                _lastPositionCheck = DateTime.Now;
                
                // Kiểm tra xem có đúng map không
                if (Player.MapID != Config.HomeMapId)
                {
                    LogInfo($"Wrong map detected ({Player.MapID} != {Config.HomeMapId}), returning home");
                    ReturnHome();
                    return;
                }
                
                // Kiểm tra khoảng cách từ vị trí gốc
                var distanceFromHome = CalculateDistance(Player.PosX, Player.PosY, Config.HomeX, Config.HomeY);
                
                if (distanceFromHome > Config.MaxRangeFromHome)
                {
                    LogDebug($"Too far from home ({distanceFromHome:F1} > {Config.MaxRangeFromHome}), returning");
                    ReturnHome();
                }
            }
            catch (Exception ex)
            {
                LogError($"Error in position validation: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Trở về vị trí gốc
        /// </summary>
        public void ReturnHome()
        {
            try
            {
                LogInfo($"Returning home to ({Config.HomeX}, {Config.HomeY}) on map {Config.HomeMapId}");
                MoveTo(Config.HomeX, Config.HomeY,Config.HomeMapId);
            }
            catch (Exception ex)
            {
                LogError($"Error returning home: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Di chuyển đến vị trí cụ thể
        /// </summary>
        /// <param name="targetX">X đích</param>
        /// <param name="targetY">Y đích</param>
        /// <param name="mapId">Map ID (optional)</param>
        public void MoveTo(float targetX, float targetY, int? mapId = null)
        {
            try
            {
                var actualMapId = mapId ?? Player.MapID;
                
                LogDebug($"Moving to ({targetX}, {targetY}) on map {actualMapId}");
                
                if (mapId.HasValue && mapId.Value != Player.MapID)
                {
                    // Cross-map movement
                    Player.Mobile(targetX, targetY, Player.PosZ, actualMapId, 0);
                }
                else
                {
                    // Same-map movement using CharacterMove
                    var distance = CalculateDistance(Player.PosX, Player.PosY, targetX, targetY);
                    MoveWithPacket(targetX, targetY, distance);
                }
            }
            catch (Exception ex)
            {
                LogError($"Error moving to ({targetX}, {targetY}): {ex.Message}");
            }
        }
        
        /// <summary>
        /// Di chuyển sử dụng packet (tương tự OfflineMove)
        /// </summary>
        /// <param name="targetX">X đích</param>
        /// <param name="targetY">Y đích</param>
        /// <param name="distance">Khoảng cách</param>
        private void MoveWithPacket(float targetX, float targetY, float distance)
        {
            try
            {
                var packetHex = "AA552E002E010700280002000000F91DC7426177ACC3978E3C44F91DC74200007041978E3C4401050000000000005C28000055AA";
                var packet = Converter.HexStringToByte(packetHex);
                
                // Set session ID
                System.Buffer.BlockCopy(BitConverter.GetBytes(Player.SessionID), 0, packet, 4, 2);
                
                // Set target position
                System.Buffer.BlockCopy(BitConverter.GetBytes(targetX), 0, packet, 14, 4);
                System.Buffer.BlockCopy(BitConverter.GetBytes(15f), 0, packet, 18, 4); // Z offset
                System.Buffer.BlockCopy(BitConverter.GetBytes(targetY), 0, packet, 22, 4);
                
                // Set current position
                System.Buffer.BlockCopy(BitConverter.GetBytes(Player.PosX), 0, packet, 26, 4);
                System.Buffer.BlockCopy(BitConverter.GetBytes(Player.PosZ), 0, packet, 30, 4);
                System.Buffer.BlockCopy(BitConverter.GetBytes(Player.PosY), 0, packet, 34, 4);
                
                // Set movement speed
                System.Buffer.BlockCopy(BitConverter.GetBytes(Player.Character_KhinhCong), 0, packet, 39, 1);
                System.Buffer.BlockCopy(BitConverter.GetBytes(distance), 0, packet, 42, 4);
                
                Player.CharacterMove(packet, packet.Length);
                
                LogDebug($"Sent movement packet to ({targetX}, {targetY})");
            }
            catch (Exception ex)
            {
                LogError($"Error sending movement packet: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Kiểm tra xem có thể di chuyển đến vị trí không
        /// </summary>
        /// <param name="targetX">X đích</param>
        /// <param name="targetY">Y đích</param>
        /// <returns>True nếu có thể di chuyển</returns>
        public bool CanMoveTo(float targetX, float targetY)
        {
            try
            {
                // Kiểm tra khoảng cách có hợp lý không
                var distance = CalculateDistance(Player.PosX, Player.PosY, targetX, targetY);
                var maxMoveDistance = GetSetting("MaxMoveDistance", 500f);
                
                if (distance > maxMoveDistance)
                {
                    LogDebug($"Move distance too far: {distance} > {maxMoveDistance}");
                    return false;
                }
                
                // Kiểm tra có trong phạm vi cho phép không
                var distanceFromHome = CalculateDistance(targetX, targetY, Config.HomeX, Config.HomeY);
                if (distanceFromHome > Config.MaxRangeFromHome)
                {
                    LogDebug($"Target too far from home: {distanceFromHome} > {Config.MaxRangeFromHome}");
                    return false;
                }
                
                return true;
            }
            catch
            {
                return false;
            }
        }
        
        /// <summary>
        /// Lấy khoảng cách từ vị trí hiện tại đến home
        /// </summary>
        /// <returns>Khoảng cách</returns>
        public float GetDistanceFromHome()
        {
            return CalculateDistance(Player.PosX, Player.PosY, Config.HomeX, Config.HomeY);
        }
        
        /// <summary>
        /// Kiểm tra xem có đang ở gần home không
        /// </summary>
        /// <param name="tolerance">Độ dung sai</param>
        /// <returns>True nếu gần home</returns>
        public bool IsNearHome(float tolerance = 50f)
        {
            return GetDistanceFromHome() <= tolerance;
        }
        
        /// <summary>
        /// Set vị trí home mới
        /// </summary>
        /// <param name="x">X mới</param>
        /// <param name="y">Y mới</param>
        /// <param name="mapId">Map ID mới</param>
        public void SetHome(float x, float y, int mapId)
        {
            Config.HomeX = x;
            Config.HomeY = y;
            Config.HomeMapId = mapId;
            
            LogInfo($"Home position updated to ({x}, {y}) on map {mapId}");
        }
    }
}
