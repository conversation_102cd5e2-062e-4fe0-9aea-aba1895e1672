using FreeSql.DataAnnotations;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;

namespace RxjhServer.Database.FreeSql.Entities.Game;

/// <summary>
/// Unified persistent party table
/// Replaces tbl_parties and tbl_party_members with a single, simpler structure
/// </summary>
[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
public partial class tbl_persistent_parties
{
    /// <summary>
    /// Auto-increment primary key
    /// </summary>
    [JsonProperty, Column(IsPrimary = true, IsIdentity = true, InsertValueSql = "nextval('tbl_persistent_parties_party_id_seq'::regclass)")]
    public int party_id { get; set; }

    /// <summary>
    /// Unique party identifier (UUID)
    /// </summary>
    [JsonProperty, Column(StringLength = 36, IsNullable = false)]
    public string party_uuid { get; set; }

    /// <summary>
    /// Current TeamID (null if party not currently active)
    /// </summary>
    [JsonProperty, Column(IsNullable = true)]
    public int? team_id { get; set; }

    /// <summary>
    /// Server ID where party was created
    /// </summary>
    [JsonProperty, Column(IsNullable = false)]
    public int server_id { get; set; }

    /// <summary>
    /// Current party leader name
    /// </summary>
    [JsonProperty, Column(StringLength = 50, IsNullable = false)]
    public string leader_name { get; set; }

    /// <summary>
    /// Loot distribution type
    /// </summary>
    [JsonProperty, Column(IsNullable = false)]
    public int loot_type { get; set; } = 1;

    /// <summary>
    /// Maximum number of members allowed
    /// </summary>
    [JsonProperty, Column(IsNullable = false)]
    public int max_members { get; set; } = 8;

    /// <summary>
    /// Party persistence mode (0=Temporary, 1=Persistent, 2=Permanent)
    /// </summary>
    [JsonProperty, Column(IsNullable = false)]
    public int persistence_mode { get; set; } = 1;

    /// <summary>
    /// When party was created
    /// </summary>
    [JsonProperty, Column(IsNullable = false, ServerTime = DateTimeKind.Local)]
    public DateTime created_at { get; set; } = DateTime.Now;

    /// <summary>
    /// Last activity timestamp
    /// </summary>
    [JsonProperty, Column(IsNullable = false, ServerTime = DateTimeKind.Local)]
    public DateTime last_active_at { get; set; } = DateTime.Now;

    /// <summary>
    /// Is party currently active
    /// </summary>
    [JsonProperty, Column(IsNullable = false)]
    public bool is_active { get; set; } = true;

    /// <summary>
    /// JSON data containing all party members (both online and offline)
    /// Structure: { "online": [...], "offline": [...] }
    /// </summary>
    [JsonProperty, Column(DbType = "jsonb", IsNullable = false)]
    public string members { get; set; } = "{}";

    /// <summary>
    /// Additional party metadata (JSON)
    /// For future extensions without schema changes
    /// </summary>
    [JsonProperty, Column(DbType = "jsonb", IsNullable = true)]
    public string metadata { get; set; }
}

/// <summary>
/// Party member data structure for JSON serialization
/// </summary>
public class PartyMemberData
{
    /// <summary>
    /// Character name
    /// </summary>
    public string characterName { get; set; }

    /// <summary>
    /// Account ID
    /// </summary>
    public int accountId { get; set; }

    /// <summary>
    /// Join order in party
    /// </summary>
    public int joinOrder { get; set; }

    /// <summary>
    /// When player joined party
    /// </summary>
    public DateTime joinedAt { get; set; }

    /// <summary>
    /// Is this player the leader
    /// </summary>
    public bool isLeader { get; set; }

    /// <summary>
    /// Player level
    /// </summary>
    public int level { get; set; }

    /// <summary>
    /// Player job/class
    /// </summary>
    public int job { get; set; }

    /// <summary>
    /// Server ID where player is/was
    /// </summary>
    public int serverId { get; set; }
}

/// <summary>
/// Online member data (extends base with session info)
/// </summary>
public class OnlinePartyMemberData : PartyMemberData
{
    /// <summary>
    /// Current session ID
    /// </summary>
    public int sessionId { get; set; }
}

/// <summary>
/// Offline member data (extends base with disconnect info)
/// </summary>
public class OfflinePartyMemberData : PartyMemberData
{
    /// <summary>
    /// When player disconnected
    /// </summary>
    public DateTime disconnectedAt { get; set; }
}

/// <summary>
/// Complete party members structure for JSON
/// </summary>
public class PartyMembersJson
{
    /// <summary>
    /// Currently online members
    /// </summary>
    public List<OnlinePartyMemberData> online { get; set; } = new();

    /// <summary>
    /// Currently offline members
    /// </summary>
    public List<OfflinePartyMemberData> offline { get; set; } = new();
}
