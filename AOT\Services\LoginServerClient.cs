using System;
using System.Net.NetworkInformation;
using System.Net.Sockets;
using System.Threading;
using System.Threading.Tasks;
using Grpc.Core;
using Grpc.Net.Client;
using HeroYulgang.Core;
using HeroYulgang.Protos;
using RxjhServer;

namespace HeroYulgang.Services
{
    public class LoginServerClient : IDisposable
    {
        private static LoginServerClient? _instance;
        private readonly ConfigManager _configManager;
        private readonly Logger _logger;
        private GrpcChannel? _channel;
        private LoginAuth.LoginAuthClient? _client;

        private AsyncDuplexStreamingCall<TransmitMessageRequest, TransmitMessageResponse>? _transmitStream;
        private bool _isConnected;
        private DateTime _lastConnectionAttempt = DateTime.MinValue;
        private readonly TimeSpan _reconnectInterval = TimeSpan.FromSeconds(1);
        private CancellationTokenSource? _receiveMessagesCancellationToken;
        private Task? _receiveMessagesTask;

        // ✅ Thread safety cho gRPC stream writes
        private readonly SemaphoreSlim _transmitSemaphore = new(1, 1);

        public bool IsConnected => _isConnected;
        public static LoginServerClient Instance => _instance ??= new LoginServerClient();

        private LoginServerClient()
        {
            _configManager = ConfigManager.Instance;
            _logger = Logger.Instance;
            _isConnected = false;
        }

        public async Task<bool> ConnectAsync()
        {
            if (_isConnected && _client != null)
            {
                return true;
            }

            // Kiểm tra thời gian kết nối lại
            if (DateTime.Now - _lastConnectionAttempt < _reconnectInterval)
            {
                return false;
            }

            _lastConnectionAttempt = DateTime.Now;

            try
            {
                var loginServerIp = _configManager.LoginServerSettings.LoginServerIP;
                var loginServerPort = _configManager.LoginServerSettings.LoginServerGrpcPort;
                var address = $"http://{loginServerIp}:{loginServerPort}";

                _logger.Info($"Đang kết nối đến LoginServer tại {address}...");

                // Tạo kênh gRPC
                _channel = GrpcChannel.ForAddress(address);
                _client = new LoginAuth.LoginAuthClient(_channel);
                _transmitStream = _client.TransmitMessage();

                // Kiểm tra kết nối bằng cách gọi một phương thức đơn giản
                await RegisterGameServerAsync();

                _isConnected = true;
                return true;
            }
            catch (Exception ex)
            {
                _isConnected = false;
                _logger.Error($"Không thể kết nối đến LoginServer: {ex.Message}");
                return false;
            }
        }

        public async Task DisconnectAsync()
        {
            // Dừng task nhận tin nhắn trước
            StopReceiveMessagesTask();

            // ✅ Đảm bảo không có operation nào đang pending trên semaphore
            await _transmitSemaphore.WaitAsync(TimeSpan.FromSeconds(2));
            try
            {
                if (_channel != null)
                {
                    await _channel.ShutdownAsync();
                    _channel = null;
                    _client = null;
                    _transmitStream = null;
                    _isConnected = false;
                    _logger.Info("Đã ngắt kết nối từ LoginServer");
                }
            }
            finally
            {
                _transmitSemaphore.Release();
            }
        }

        public async Task<bool> RegisterGameServerAsync()
        {
            if (_client == null)
            {
                _logger.Error("Không thể đăng ký GameServer: Chưa kết nối đến LoginServer");
                return false;
            }

            try
            {
                var serverConfig = _configManager.ServerSettings;
                var loginConfig = _configManager.LoginServerSettings;

                // Lấy địa chỉ IP của máy chủ
                string serverIp = GetLocalIPAddress();

                var request = new RegisterGameServerRequest
                {
                    ClusterId = loginConfig.ClusterId,
                    ServerId = serverConfig.ServerId,
                    ServerName = serverConfig.ServerName,
                    ServerIp = serverIp,
                    ServerPort = serverConfig.GameServerPort,
                    GrpcPort = serverConfig.GrpcPort // AOT cung cấp gRPC service
                };

                var response = await _client.RegisterGameServerAsync(request);

                if (response.Success)
                {
                    _logger.Info($"Đăng ký GameServer thành công: {response.Message}");
                    return true;
                }
                else
                {
                    _logger.Error($"Đăng ký GameServer thất bại: {response.Message}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _isConnected = false;
                _logger.Error($"Lỗi khi đăng ký GameServer: {ex.Message}");
                return false;
            }
        }

        public async Task SendTransmitMessageAsync(string message)
        {
            if (_transmitStream == null)
            {
                _logger.Error("Không thể gửi tin nhắn Transmit: Chưa kết nối đến LoginServer");
                return;
            }

            // ✅ Thread safety với timeout để tránh deadlock
            if (!await _transmitSemaphore.WaitAsync(TimeSpan.FromSeconds(5)))
            {
                _logger.Warning($"Timeout khi chờ gửi tin nhắn Transmit: {message}");
                return;
            }

            try
            {
                _logger.Debug($"Sending to LS: {message}");
                var request = new TransmitMessageRequest
                {
                    ClusterId = _configManager.LoginServerSettings.ClusterId,
                    ServerId = _configManager.ServerSettings.ServerId,
                    Message = message
                };

                await _transmitStream.RequestStream.WriteAsync(request);
                _logger.Debug($"Successfully sent to LS: {message}");
            }
            catch (Exception ex)
            {
                _logger.Error($"Lỗi khi gửi tin nhắn Transmit: {ex.Message}");
            }
            finally
            {
                // ✅ Luôn release semaphore trong finally block
                _transmitSemaphore.Release();
            }
        }

        public async Task ReceiveMessagesAsync()
        {
            if (_transmitStream == null)
            {
                _logger.Error("Không thể nhận tin nhắn: Chưa kết nối đến LoginServer");
                return;
            }

            try
            {
                _logger.Debug("Bắt đầu lắng nghe tin nhắn từ LoginServer...");

                while (await _transmitStream.ResponseStream.MoveNext())
                {
                    var response = _transmitStream.ResponseStream.Current;
                    _logger.Debug($"Nhận từ LS: {response.Message}, Success: {response.Success}");

                    // Xử lý message ở đây nếu cần
                    ProcessReceivedMessage(response);
                }
                _logger.Debug("Done receiving messages from LoginServer...");
            }
            catch (RpcException ex) when (ex.StatusCode == StatusCode.Cancelled)
            {
                _logger.Error("Dừng nhận tin nhắn từ LoginServer (đã hủy)");
            }
            catch (RpcException ex)
            {
                _logger.Error($"Lỗi gRPC khi nhận tin nhắn từ LoginServer: {ex.Status}");
                _isConnected = false;

                // Thử kết nối lại sau một khoảng thời gian
                _ = Task.Run(async () =>
                {
                    await Task.Delay(TimeSpan.FromSeconds(1));
                    await TryReconnectAsync();
                });
            }
            catch (Exception ex)
            {
                _logger.Error($"Lỗi không mong đợi khi nhận tin nhắn từ LoginServer: {ex.Message}");
                _isConnected = false;
            }
        }

        private void ProcessReceivedMessage(TransmitMessageResponse response)
        {
            try
            {
                // Xử lý message từ LoginServer ở đây
                // Có thể parse message và thực hiện các hành động tương ứng
                _logger.Debug($"Xử lý message: {response.Message}");
                // Console.WriteLine($"Xử lý message: {response.Message}");
                World.conn.DataReceived(response.Message, response.Message.Length);
            }
            catch (Exception ex)
            {
                _logger.Error($"Lỗi khi xử lý message từ LoginServer: {ex.Message}");
            }
        }

        private async Task TryReconnectAsync()
        {
            _logger.Info("Đang thử kết nối lại đến LoginServer...");

            var reconnected = await ConnectAsync();
            if (reconnected)
            {
                _logger.Info("Kết nối lại thành công, bắt đầu lắng nghe tin nhắn...");
                StartReceiveMessagesTask();
            }
            else
            {
                _logger.Warning("Không thể kết nối lại đến LoginServer");
            }
        }

        public void StartReceiveMessagesTask()
        {
            // Dừng task cũ nếu có
            StopReceiveMessagesTask();

            if (_transmitStream == null)
            {
                _logger.Warning("Không thể bắt đầu nhận tin nhắn: Chưa có stream");
                return;
            }

            _receiveMessagesCancellationToken = new CancellationTokenSource();
            _receiveMessagesTask = Task.Run(async () =>
            {
                try
                {
                    await ReceiveMessagesAsync();
                }
                catch (Exception ex)
                {
                    _logger.Error($"Lỗi trong task nhận tin nhắn: {ex.Message}");
                }
            }, _receiveMessagesCancellationToken.Token);

            _logger.Info("Đã bắt đầu task nhận tin nhắn từ LoginServer");
        }

        public void StopReceiveMessagesTask()
        {
            try
            {
                _receiveMessagesCancellationToken?.Cancel();
                _receiveMessagesTask?.Wait(TimeSpan.FromSeconds(5));
            }
            catch (Exception ex)
            {
                _logger.Warning($"Lỗi khi dừng task nhận tin nhắn: {ex.Message}");
            }
            finally
            {
                _receiveMessagesCancellationToken?.Dispose();
                _receiveMessagesCancellationToken = null;
                _receiveMessagesTask = null;
            }

            _logger.Info("Đã dừng task nhận tin nhắn từ LoginServer");
        }
        public async Task CloseStreamAsync()
        {
            await _transmitStream.RequestStream.CompleteAsync();
        }

        public async Task<ValidateAccountLoginResponse> ValidateAccountLoginAsync(string accountId, string password, string userIp, int userPort, string lanIp, int lanPort)
        {
            if (_client == null)
            {
                _logger.Error("Không thể xác thực tài khoản: Chưa kết nối đến LoginServer");
                return new ValidateAccountLoginResponse { IsValid = false, ErrorMessage = "Chưa kết nối đến LoginServer" };
            }

            try
            {
                var serverConfig = _configManager.ServerSettings;
                var loginConfig = _configManager.LoginServerSettings;

                var request = new ValidateAccountLoginRequest
                {
                    AccountId = accountId,
                    Password = password,
                    ClusterId = loginConfig.ClusterId,
                    ServerId = serverConfig.ServerId,
                    UserIp = userIp,
                    UserPort = userPort,
                    LanIp = lanIp,
                    LanPort = lanPort
                };

                var response = await _client.ValidateAccountLoginAsync(request);

                if (response.IsValid)
                {
                    _logger.Info($"Xác thực tài khoản thành công cho tài khoản {accountId} tới {loginConfig.ClusterId}:{serverConfig.ServerId}");
                    return response;
                }
                else
                {
                    _logger.Warning($"Xác thực tài khoản thất bại cho tài khoản {accountId}: {response.ErrorMessage}");
                    return response;
                }
            }
            catch (Exception ex)
            {
                _isConnected = false;
                _logger.Error($"Lỗi khi xác thực tài khoản: {ex.Message}");
                return new ValidateAccountLoginResponse { IsValid = false, ErrorMessage = "Lỗi hệ thống" };
            }
        }

        public async Task<bool> UpdateGameServerStatusAsync(int onlineCount, bool isOnline)
        {
            if (_client == null)
            {
                _logger.Error("Không thể cập nhật trạng thái GameServer: Chưa kết nối đến LoginServer");
                return false;
            }

            try
            {
                var serverConfig = _configManager.ServerSettings;
                var loginConfig = _configManager.LoginServerSettings;

                var request = new UpdateGameServerStatusRequest
                {
                    ClusterId = loginConfig.ClusterId,
                    ServerId = serverConfig.ServerId,
                    OnlineCount = onlineCount,
                    IsOnline = isOnline
                };

                var response = await _client.UpdateGameServerStatusAsync(request);

                if (response.Success)
                {
                    //_logger.Debug($"Cập nhật trạng thái GameServer thành công: {response.Message}");
                    return true;
                }
                else
                {
                    _logger.Warning($"Cập nhật trạng thái GameServer thất bại: {response.Message}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _isConnected = false;
                _logger.Error($"Lỗi khi cập nhật trạng thái GameServer: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> VerifyTokenAsync(string accountId, string token)
        {
            if (_client == null)
            {
                _logger.Error("Không thể xác thực token: Chưa kết nối đến LoginServer");
                return false;
            }

            try
            {
                var request = new VerifyTokenRequest
                {
                    AccountId = accountId,
                    Token = token
                };

                var response = await _client.VerifyTokenAsync(request);

                if (response.IsValid)
                {
                    _logger.Info($"Xác thực token thành công cho tài khoản {accountId}");
                    return true;
                }
                else
                {
                    _logger.Warning($"Xác thực token thất bại cho tài khoản {accountId}: {response.ErrorMessage}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _isConnected = false;
                _logger.Error($"Lỗi khi xác thực token: {ex.Message}");
                return false;
            }
        }

        private string GetLocalIPAddress()
        {
            string localIP = "127.0.0.1";
            try
            {
                // Lấy địa chỉ IP của máy chủ
                using (Socket socket = new Socket(AddressFamily.InterNetwork, SocketType.Dgram, 0))
                {
                    socket.Connect("*******", 65530);
                    if (socket.LocalEndPoint is System.Net.IPEndPoint endPoint)
                    {
                        localIP = endPoint.Address.ToString();
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Warning($"Không thể lấy địa chỉ IP: {ex.Message}. Sử dụng 127.0.0.1");
            }
            return localIP;
        }

        // ✅ IDisposable implementation để cleanup SemaphoreSlim
        private bool _disposed = false;

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    // Dispose managed resources
                    _transmitSemaphore?.Dispose();
                    _receiveMessagesCancellationToken?.Dispose();
                    _channel?.Dispose();
                }
                _disposed = true;
            }
        }
    }
}
