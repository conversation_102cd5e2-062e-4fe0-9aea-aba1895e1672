using HeroYulgang.Helpers;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;


namespace RxjhServer.AOI
{
    /// <summary>
    /// Represents a spatial grid for Area of Interest (AOI) management
    /// Each grid covers a 1024x1024 area of the game world
    /// </summary>
    public class AOIGrid
    {
        #region Properties
        
        /// <summary>
        /// Grid X coordinate (0-4 for 5x5 grid system)
        /// </summary>
        public int GridX { get; set; }
        
        /// <summary>
        /// Grid Y coordinate (0-4 for 5x5 grid system)
        /// </summary>
        public int GridY { get; set; }
        
        /// <summary>
        /// Map ID this grid belongs to
        /// </summary>
        public int MapID { get; set; }
        
        #endregion
        
        #region World Boundaries
        
        /// <summary>
        /// Minimum X coordinate in world space
        /// </summary>
        public float MinX { get; set; }
        
        /// <summary>
        /// Maximum X coordinate in world space
        /// </summary>
        public float MaxX { get; set; }
        
        /// <summary>
        /// Minimum Y coordinate in world space
        /// </summary>
        public float MinY { get; set; }
        
        /// <summary>
        /// Maximum Y coordinate in world space
        /// </summary>
        public float MaxY { get; set; }
        
        #endregion
        
        #region Overlap Boundaries
        
        /// <summary>
        /// Extended minimum X coordinate including overlap zone
        /// </summary>
        public float OverlapMinX { get; set; }
        
        /// <summary>
        /// Extended maximum X coordinate including overlap zone
        /// </summary>
        public float OverlapMaxX { get; set; }
        
        /// <summary>
        /// Extended minimum Y coordinate including overlap zone
        /// </summary>
        public float OverlapMinY { get; set; }
        
        /// <summary>
        /// Extended maximum Y coordinate including overlap zone
        /// </summary>
        public float OverlapMaxY { get; set; }
        
        #endregion
        
        #region Entity Containers - Reference Based (Optimized)

        /// <summary>
        /// Player IDs currently in this grid (references to World.allConnectedChars)
        /// Thread-safe using ConcurrentDictionary as a set
        /// </summary>
        private readonly ConcurrentDictionary<int, byte> _playerIDs;

        /// <summary>
        /// NPC IDs currently in this grid (references to World.NpcList)
        /// </summary>
        private readonly HashSet<int> _npcSessionIDs;

        /// <summary>
        /// Ground item IDs currently in this grid (references to World.GroundItemList)
        /// Thread-safe using ConcurrentDictionary as a set
        /// </summary>
        private readonly ConcurrentDictionary<long, byte> _groundItemIDs;

        // No longer needed - ConcurrentDictionary is thread-safe
        // private readonly ReaderWriterLockSlim _playersLock;

        /// <summary>
        /// Lock for thread-safe access to NPC IDs
        /// </summary>
        private readonly ReaderWriterLockSlim _npcsLock;

        /// <summary>
        /// Lock for thread-safe access to ground item IDs
        /// </summary>
        // private readonly ReaderWriterLockSlim _itemsLock;

        /// <summary>
        /// Players currently in this grid (live data from World.allConnectedChars)
        /// </summary>
        public IEnumerable<Players> Players
        {
            get
            {
                // No lock needed - ConcurrentDictionary is thread-safe
                foreach (var playerID in _playerIDs.Keys)
                {
                    if (World.allConnectedChars.TryGetValue(playerID, out var player))
                    {
                        yield return player;
                    }
                }
            }
        }

        /// <summary>
        /// Get the count of players currently in this grid (optimized)
        /// </summary>
        public int PlayerCount
        {
            get
            {
                // No lock needed - ConcurrentDictionary.Count is thread-safe
                return _playerIDs.Count;
            }
        }

        /// <summary>
        /// NPCs currently in this grid (live data from World.NpcList)
        /// </summary>
        public IEnumerable<NpcClass> NPCs
        {
            get
            {
                _npcsLock.EnterReadLock();
                try
                {
                    foreach (var npcID in _npcSessionIDs)
                    {
                        if (MapClass.GetnpcTemplate(MapID).TryGetValue(npcID, out var npc))
                        {
                            yield return npc;
                        }
                    }
                }
                finally
                {
                    _npcsLock.ExitReadLock();
                }
            }
        }

        /// <summary>
        /// Ground items currently in this grid (live data from World.ItmeTeM)
        /// </summary>
        public IEnumerable<GroundItem> GroundItems
        {
            get
            {
                // No lock needed - ConcurrentDictionary enumeration is thread-safe
                foreach (var itemID in _groundItemIDs.Keys)
                {
                    if (World.GroundItemList.TryGetValue(itemID, out var item))
                    {
                        yield return item;
                    }
                }
            }
        }

        #endregion
        
        #region Adjacent Grid Management
        
        /// <summary>
        /// List of adjacent grids for AOI calculations
        /// Includes all 8 surrounding grids (if they exist)
        /// </summary>
        public List<AOIGrid> AdjacentGrids { get; set; }
        
        #endregion
        
        #region Performance Optimization
        
        /// <summary>
        /// Flag indicating if this grid has been modified and needs updates
        /// </summary>
        public bool IsDirty { get; set; }
        
        /// <summary>
        /// Timestamp of last update for this grid
        /// </summary>
        public DateTime LastUpdate { get; set; }
        
        /// <summary>
        /// Total number of entities in this grid
        /// </summary>
        public int EntityCount
        {
            get
            {
                // Players and ground items collections are thread-safe, but still need lock for NPCs
                _npcsLock.EnterReadLock();
                try
                {
                    return _playerIDs.Count + _npcSessionIDs.Count + _groundItemIDs.Count;
                }
                finally
                {
                    _npcsLock.ExitReadLock();
                }
            }
        }
        
        #endregion
        
        #region Constructor
        
        /// <summary>
        /// Initialize a new AOI grid
        /// </summary>
        public AOIGrid()
        {
            _playerIDs = new ConcurrentDictionary<int, byte>();
            _npcSessionIDs = new HashSet<int>();
            _groundItemIDs = new ConcurrentDictionary<long, byte>();

            // _playersLock = new ReaderWriterLockSlim(); // No longer needed
            _npcsLock = new ReaderWriterLockSlim();
            // _itemsLock = new ReaderWriterLockSlim();

            AdjacentGrids = new List<AOIGrid>();
            IsDirty = false;
            LastUpdate = DateTime.Now;
        }
        
        /// <summary>
        /// Initialize a new AOI grid with specific coordinates
        /// </summary>
        /// <param name="gridX">Grid X coordinate</param>
        /// <param name="gridY">Grid Y coordinate</param>
        /// <param name="mapID">Map ID</param>
        public AOIGrid(int gridX, int gridY, int mapID) : this()
        {
            GridX = gridX;
            GridY = gridY;
            MapID = mapID;
        }
        
        #endregion
        
        #region Entity Management
        
        /// <summary>
        /// Add a player to this grid
        /// </summary>
        /// <param name="player">Player to add</param>
        /// <returns>True if added successfully</returns>
        public bool AddPlayer(Players player)
        {
            try
            {
                // Use TryAdd for thread-safe addition
                if (_playerIDs.TryAdd(player.SessionID, 0))
                {
                    IsDirty = true;
                    //   LogHelper.WriteLine(LogLevel.Info, $"Player {player.UserName} added to grid ({GridX},{GridY}) on map {MapID}");
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error adding player to grid: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Remove a player from this grid
        /// </summary>
        /// <param name="sessionID">Player session ID</param>
        /// <returns>True if removed successfully</returns>
        public bool RemovePlayer(int sessionID)
        {
            try
            {
                // Use TryRemove for thread-safe removal
                if (_playerIDs.TryRemove(sessionID, out _))
                {
                    IsDirty = true;
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error removing player from grid: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// Add an NPC to this grid
        /// </summary>
        /// <param name="npc">NPC to add</param>
        /// <returns>True if added successfully</returns>
        public bool AddNPC(NpcClass npc)
        {
            try
            {
                _npcsLock.EnterWriteLock();
                try
                {
                    if (_npcSessionIDs.Add(npc.NPC_SessionID))
                    {
                        IsDirty = true;
                       // LogHelper.WriteLine(LogLevel.Info, $"NPC {npc.Name} added to grid ({GridX},{GridY}) on map {MapID}");
                        return true;
                    }
                    return false;
                }
                finally
                {
                    _npcsLock.ExitWriteLock();
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error adding NPC to grid: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Remove an NPC from this grid
        /// </summary>
        /// <param name="npcSessionID">NPC session ID</param>
        /// <returns>True if removed successfully</returns>
        public bool RemoveNPC(int npcSessionID)
        {
            try
            {
                _npcsLock.EnterWriteLock();
                try
                {
                    if (_npcSessionIDs.Remove(npcSessionID))
                    {
                        IsDirty = true;
                        // Get NPC name for logging if still exists in global collection
                        var npcName = MapClass.GetnpcTemplate(MapID).TryGetValue(npcSessionID, out var npc) ? npc.Name : "Unknown";
                       // LogHelper.WriteLine(LogLevel.Info, $"NPC {npcName} removed from grid ({GridX},{GridY}) on map {MapID}");
                        return true;
                    }
                    return false;
                }
                finally
                {
                    _npcsLock.ExitWriteLock();
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error removing NPC from grid: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// Add a ground item to this grid
        /// </summary>
        /// <param name="item">Ground item to add</param>
        /// <returns>True if added successfully</returns>
        public bool AddGroundItem(GroundItem item)
        {
            try
            {
                // Use TryAdd for thread-safe addition
                if (_groundItemIDs.TryAdd(item.id, 0))
                {
                    IsDirty = true;
                  //  LogHelper.WriteLine(LogLevel.Info, $"Ground item {item.id} added to grid ({GridX},{GridY}) on map {MapID}");
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error adding ground item to grid: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Remove a ground item from this grid
        /// </summary>
        /// <param name="itemID">Ground item ID</param>
        /// <returns>True if removed successfully</returns>
        public bool RemoveGroundItem(long itemID)
        {
            try
            {
                // Use TryRemove for thread-safe removal
                if (_groundItemIDs.TryRemove(itemID, out _))
                {
                    IsDirty = true;
                  //  LogHelper.WriteLine(LogLevel.Info, $"Ground item {itemID} removed from grid ({GridX},{GridY}) on map {MapID}");
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error removing ground item from grid: {ex.Message}");
                return false;
            }
        }
        
        #endregion

        #region Optimized Entity Access Methods

        /// <summary>
        /// Execute an action for each player in this grid (optimized enumeration)
        /// </summary>
        /// <param name="action">Action to execute for each player</param>
        public void ForEachPlayer(Action<Players> action)
        {
            // No lock needed - ConcurrentDictionary enumeration is thread-safe
            foreach (var playerID in _playerIDs.Keys)
            {
                if (World.allConnectedChars.TryGetValue(playerID, out var player))
                    action(player);
            }
        }

        /// <summary>
        /// Execute an action for each NPC in this grid (optimized enumeration)
        /// </summary>
        /// <param name="action">Action to execute for each NPC</param>
        public void ForEachNPC(Action<NpcClass> action)
        {
            _npcsLock.EnterReadLock();
            try
            {
                foreach (var npcID in _npcSessionIDs)
                {
                    if (MapClass.GetnpcTemplate(MapID).TryGetValue(npcID, out var npc))
                    {
                        action(npc);
                    }
                }
            }
            finally
            {
                _npcsLock.ExitReadLock();
            }
        }

        /// <summary>
        /// Execute an action for each ground item in this grid (optimized enumeration)
        /// </summary>
        /// <param name="action">Action to execute for each ground item</param>
        public void ForEachGroundItem(Action<GroundItem> action)
        {
            // No lock needed - ConcurrentDictionary enumeration is thread-safe
            foreach (var itemID in _groundItemIDs.Keys)
            {
                if (World.GroundItemList.TryGetValue(itemID, out var item))
                {
                    action(item);
                }
            }
        }

        /// <summary>
        /// Check if a player is in this grid
        /// </summary>
        /// <param name="sessionID">Player session ID</param>
        /// <returns>True if player is in this grid</returns>
        public bool ContainsPlayer(int sessionID)
        {
            // No lock needed - ConcurrentDictionary.ContainsKey is thread-safe
            return _playerIDs.ContainsKey(sessionID);
        }

        /// <summary>
        /// Check if an NPC is in this grid
        /// </summary>
        /// <param name="npcSessionID">NPC session ID</param>
        /// <returns>True if NPC is in this grid</returns>
        public bool ContainsNPC(int npcSessionID)
        {
            _npcsLock.EnterReadLock();
            try
            {
                return _npcSessionIDs.Contains(npcSessionID);
            }
            finally
            {
                _npcsLock.ExitReadLock();
            }
        }

        /// <summary>
        /// Check if a ground item is in this grid
        /// </summary>
        /// <param name="itemID">Ground item ID</param>
        /// <returns>True if ground item is in this grid</returns>
        public bool ContainsGroundItem(long itemID)
        {
            // No lock needed - ConcurrentDictionary.ContainsKey is thread-safe
            return _groundItemIDs.ContainsKey(itemID);
        }

        /// <summary>
        /// Get players within a specific range from a center point (optimized)
        /// </summary>
        /// <param name="centerX">Center X coordinate</param>
        /// <param name="centerY">Center Y coordinate</param>
        /// <param name="range">Range to search within</param>
        /// <returns>List of players within range</returns>
        public List<Players> GetPlayersInRange(float centerX, float centerY, float range)
        {
            var result = new List<Players>();
            var rangeSquared = range * range; // Use squared distance to avoid sqrt calculation

            // No lock needed - ConcurrentDictionary enumeration is thread-safe
            foreach (var playerID in _playerIDs.Keys)
            {
                if (World.allConnectedChars.TryGetValue(playerID, out var player))
                {
                    var deltaX = player.PosX - centerX;
                    var deltaY = player.PosY - centerY;
                    var distanceSquared = deltaX * deltaX + deltaY * deltaY;

                    if (distanceSquared <= rangeSquared)
                    {
                        result.Add(player);
                    }
                }
            }

            return result;
        }

        #endregion

        #region Utility Methods
        
        /// <summary>
        /// Check if a world position is within this grid's boundaries
        /// </summary>
        /// <param name="x">World X coordinate</param>
        /// <param name="y">World Y coordinate</param>
        /// <returns>True if position is within grid boundaries</returns>
        public bool ContainsPosition(float x, float y)
        {
            return x >= MinX && x < MaxX && y >= MinY && y < MaxY;
        }
        
        /// <summary>
        /// Check if a world position is within this grid's overlap boundaries
        /// </summary>
        /// <param name="x">World X coordinate</param>
        /// <param name="y">World Y coordinate</param>
        /// <returns>True if position is within overlap boundaries</returns>
        public bool ContainsPositionWithOverlap(float x, float y)
        {
            return x >= OverlapMinX && x < OverlapMaxX && y >= OverlapMinY && y < OverlapMaxY;
        }
        
        /// <summary>
        /// Mark this grid as clean (no longer dirty)
        /// </summary>
        public void MarkClean()
        {
            IsDirty = false;
            LastUpdate = DateTime.Now;
        }
        
        /// <summary>
        /// Get string representation of this grid
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AOIGrid({GridX},{GridY}) Map:{MapID} Entities:{EntityCount} Dirty:{IsDirty}";
        }

        /// <summary>
        /// Dispose resources used by this grid
        /// </summary>
        public void Dispose()
        {
            try
            {
                // _playersLock?.Dispose(); // No longer needed
                _npcsLock?.Dispose();
                // _itemsLock?.Dispose(); // No longer needed
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error disposing AOIGrid resources: {ex.Message}");
            }
        }

        #endregion
    }
}
