using System;
using System.Collections.Concurrent;
using System.Threading;
using System.Threading.Tasks;
using HeroYulgang.Helpers;
using RxjhServer.TestBot.Modules.Core;

namespace RxjhServer.TestBot.Core
{
    /// <summary>
    /// Manager ch<PERSON>h cho Test Bot system
    /// Quản lý việc chuyển đổi giữa hệ thống cũ và mới
    /// </summary>
    public class TestBotManager : IDisposable
    {
        private static TestBotManager _instance;
        private static readonly object _lock = new object();
        
        private readonly ConcurrentDictionary<int, TestBotCore> _activeBots = new ConcurrentDictionary<int, TestBotCore>();
        private readonly Timer _updateTimer;
        private bool _disposed = false;
        
        /// <summary>
        /// Singleton instance
        /// </summary>
        public static TestBotManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new TestBotManager();
                        }
                    }
                }
                return _instance;
            }
        }
        
        /// <summary>
        /// Số lượng bot đang hoạt động
        /// </summary>
        public int ActiveBotCount => _activeBots.Count;
        
        /// <summary>
        /// Private constructor cho singleton
        /// </summary>
        private TestBotManager()
        {
            // Tạo timer để update tất cả bot
            _updateTimer = new Timer(UpdateAllBots, null, TimeSpan.FromSeconds(1), TimeSpan.FromSeconds(1));
            LogHelper.WriteLine(LogLevel.Info, "[TestBotManager] Initialized");
        }
        
        /// <summary>
        /// Bắt đầu bot cho player (hệ thống mới)
        /// </summary>
        /// <param name="player">Player instance</param>
        /// <param name="config">Bot configuration (optional)</param>
        /// <returns>True nếu thành công</returns>
        public bool StartBot(Players player, BotConfig config = null)
        {
            if (player == null)
                return false;
                
            try
            {
                // Kiểm tra xem đã có bot cho player này chưa
                if (_activeBots.ContainsKey(player.SessionID))
                {
                    LogHelper.WriteLine(LogLevel.Warning, $"[TestBotManager] Bot already exists for player {player.CharacterName}");
                    return false;
                }
                
                // Tạo config mặc định nếu không có
                if (config == null)
                {
                    config = BotConfig.CreateDefault(player);
                }
                
                // Tạo bot core
                var botCore = new TestBotCore(player, config);
                
                // Đăng ký các module cơ bản
                RegisterCoreModules(botCore);
                
                // Thêm vào danh sách active bots
                if (_activeBots.TryAdd(player.SessionID, botCore))
                {
                    // Bắt đầu bot
                    botCore.Start();
                    
                    // Đăng ký event handlers
                    botCore.OnBotStopped += OnBotStopped;
                    botCore.OnError += OnBotError;
                    
                    LogHelper.WriteLine(LogLevel.Info, $"[TestBotManager] Started new bot for player {player.CharacterName}");
                    return true;
                }
                else
                {
                    botCore.Dispose();
                    return false;
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"[TestBotManager] Error starting bot for player {player.CharacterName}: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// Dừng bot cho player
        /// </summary>
        /// <param name="sessionId">Session ID của player</param>
        /// <returns>True nếu thành công</returns>
        public bool StopBot(int sessionId)
        {
            try
            {
                if (_activeBots.TryRemove(sessionId, out var botCore))
                {
                    botCore.Stop();
                    botCore.Dispose();
                    LogHelper.WriteLine(LogLevel.Info, $"[TestBotManager] Stopped bot for session {sessionId}");
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"[TestBotManager] Error stopping bot for session {sessionId}: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// Lấy bot instance cho player
        /// </summary>
        /// <param name="sessionId">Session ID của player</param>
        /// <returns>Bot instance hoặc null</returns>
        public TestBotCore GetBot(int sessionId)
        {
            return _activeBots.TryGetValue(sessionId, out var bot) ? bot : null;
        }
        
        /// <summary>
        /// Kiểm tra xem player có bot đang chạy không
        /// </summary>
        /// <param name="sessionId">Session ID của player</param>
        /// <returns>True nếu có bot đang chạy</returns>
        public bool HasActiveBot(int sessionId)
        {
            return _activeBots.ContainsKey(sessionId);
        }
        
        /// <summary>
        /// Xử lý offline training (tương thích với hệ thống cũ)
        /// </summary>
        /// <param name="player">Player instance</param>
        public void ProcessOfflineTraining(Players player)
        {
            if (player == null)
                return;
                
            try
            {
                // Kiểm tra xem có bot mới không
                if (_activeBots.TryGetValue(player.SessionID, out var botCore))
                {
                    // Sử dụng hệ thống bot mới
                    botCore.Update();
                }
                else
                {
                    // Fallback về hệ thống cũ
                    Offline_Treo_May.OfflineTreoMay(player);
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"[TestBotManager] Error processing offline training for {player.CharacterName}: {ex.Message}");
                
                // Fallback về hệ thống cũ nếu có lỗi
                try
                {
                    Offline_Treo_May.OfflineTreoMay(player);
                }
                catch (Exception fallbackEx)
                {
                    LogHelper.WriteLine(LogLevel.Error, $"[TestBotManager] Fallback also failed: {fallbackEx.Message}");
                }
            }
        }
        
        /// <summary>
        /// Đăng ký các module cơ bản
        /// </summary>
        /// <param name="botCore">Bot core instance</param>
        private void RegisterCoreModules(TestBotCore botCore)
        {
            try
            {
                // Đăng ký các module theo thứ tự ưu tiên
                botCore.RegisterModule(new StatusModule());      // Priority 1
                botCore.RegisterModule(new MovementModule());    // Priority 2  
                botCore.RegisterModule(new BuffModule());        // Priority 3
                botCore.RegisterModule(new ItemModule());        // Priority 4
                botCore.RegisterModule(new CombatModule());      // Priority 5
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"[TestBotManager] Error registering core modules: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Update tất cả bot (được gọi bởi timer)
        /// </summary>
        /// <param name="state">Timer state</param>
        private void UpdateAllBots(object state)
        {
            if (_disposed)
                return;
                
            try
            {
                Parallel.ForEach(_activeBots.Values, bot =>
                {
                    try
                    {
                        if (bot.IsRunning)
                        {
                            bot.Update();
                        }
                    }
                    catch (Exception ex)
                    {
                        LogHelper.WriteLine(LogLevel.Error, $"[TestBotManager] Error updating bot: {ex.Message}");
                    }
                });
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"[TestBotManager] Error in update loop: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Event handler khi bot dừng
        /// </summary>
        /// <param name="botCore">Bot instance</param>
        private void OnBotStopped(IBotCore botCore)
        {
            try
            {
                if (botCore?.Player != null)
                {
                    _activeBots.TryRemove(botCore.Player.SessionID, out _);
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"[TestBotManager] Error handling bot stopped event: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Event handler khi bot có lỗi
        /// </summary>
        /// <param name="botCore">Bot instance</param>
        /// <param name="exception">Exception</param>
        private void OnBotError(IBotCore botCore, Exception exception)
        {
            LogHelper.WriteLine(LogLevel.Error, $"[TestBotManager] Bot error for {botCore?.Player?.CharacterName}: {exception.Message}");
        }
        
        /// <summary>
        /// Dispose resources
        /// </summary>
        public void Dispose()
        {
            if (_disposed)
                return;
                
            try
            {
                _disposed = true;
                
                // Dừng timer
                _updateTimer?.Dispose();
                
                // Dừng tất cả bot
                foreach (var bot in _activeBots.Values)
                {
                    try
                    {
                        bot.Stop();
                        bot.Dispose();
                    }
                    catch (Exception ex)
                    {
                        LogHelper.WriteLine(LogLevel.Error, $"[TestBotManager] Error disposing bot: {ex.Message}");
                    }
                }
                
                _activeBots.Clear();
                LogHelper.WriteLine(LogLevel.Info, "[TestBotManager] Disposed");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"[TestBotManager] Error during disposal: {ex.Message}");
            }
        }
    }
}
