using System;
using System.Collections.Generic;

public class AttendancePacketParser
{
    public static void ParseAttendancePacket()
    {
        // Packet data từ 0x12 onwards
        string packetHex = "8a0100000100000008dd143c0000000001000000000051e6143c0000000001000000000052e6143c0000000001000000000053e6143c0000000001000000000054e6143c00000000010000000000e0cf9a3b00000000320000000000cf99053c0000000001000000000009dd143c0000000001000000000055e6143c0000000001000000000056e6143c0000000001000000000057e6143c0000000001000000000058e6143c00000000010000000000e0cf9a3b00000000640000000000d599053c00000000010000000000f3dd143c0000000001000000000076e0143c000000000100000000005ee4143c00000000010000000000cde3143c000000000100000000000be2143c000000000100000000000ce2143c0000000001000000000011e0143c00000000010000000000d2e2143c000000000100000000000ae6143c0000000001000000000059e6143c00000000010000000000d1e5143c00000000010000000000dae5143c000000000100000000005ddd143c000000000100000000002ee2143c00000000010000000000";

        // Convert hex string to bytes
        byte[] data = HexStringToByteArray(packetHex);
        
        var items = new List<(int itemId, int amount)>();
        
        // Parse 28 items
        for (int i = 0; i < 28; i++)
        {
            int offset = i * 14; // Mỗi item có 14 bytes (4 + 4 + 4 + 2)
            
            if (offset + 12 <= data.Length)
            {
                // 4 bytes đầu: ItemId (little endian)
                int itemId = BitConverter.ToInt32(data, offset);
                
                // Skip 4 bytes padding
                
                // 4 bytes tiếp: Amount (little endian)
                int amount = BitConverter.ToInt32(data, offset + 8);
                
                items.Add((itemId, amount));
                
                Console.WriteLine($"Day {i + 1}: ItemId = {itemId} (0x{itemId:X8}), Amount = {amount}");
            }
        }
        
        // Generate SQL
        GenerateAttendanceSQL(items);
    }
    
    public static void GenerateAttendanceSQL(List<(int itemId, int amount)> items)
    {
        Console.WriteLine("\n=== SQL QUERY ===\n");
        
        // Tạo attendance template
        Console.WriteLine("-- Tạo attendance template mới");
        Console.WriteLine("INSERT INTO tbl_attendance_templates (name, month, year, is_active, start_date, end_date)");
        Console.WriteLine("VALUES (");
        Console.WriteLine($"    'Điểm danh tháng {DateTime.Now.Month}/{DateTime.Now.Year}',");
        Console.WriteLine($"    {DateTime.Now.Month},");
        Console.WriteLine($"    {DateTime.Now.Year},");
        Console.WriteLine("    true,");
        Console.WriteLine($"    '{DateTime.Now:yyyy-MM-01}',");
        Console.WriteLine($"    '{DateTime.Now.AddMonths(1).AddDays(-1):yyyy-MM-dd}'");
        Console.WriteLine(");");
        Console.WriteLine();
        
        // Lấy ID của attendance vừa tạo
        Console.WriteLine("-- Lấy ID của attendance template vừa tạo");
        Console.WriteLine("DO $$");
        Console.WriteLine("DECLARE");
        Console.WriteLine("    template_id INTEGER;");
        Console.WriteLine("BEGIN");
        Console.WriteLine("    -- Lấy ID của template vừa tạo");
        Console.WriteLine("    SELECT id INTO template_id");
        Console.WriteLine("    FROM tbl_attendance_templates");
        Console.WriteLine($"    WHERE month = {DateTime.Now.Month} AND year = {DateTime.Now.Year}");
        Console.WriteLine("    ORDER BY created_date DESC");
        Console.WriteLine("    LIMIT 1;");
        Console.WriteLine();
        
        // Thêm rewards
        Console.WriteLine("    -- Thêm 28 rewards");
        for (int i = 0; i < items.Count && i < 28; i++)
        {
            var item = items[i];
            Console.WriteLine($"    INSERT INTO tbl_attendance_rewards (attendance_id, day_number, item_id, item_amount)");
            Console.WriteLine($"    VALUES (template_id, {i + 1}, {item.itemId}, {item.amount});");
        }
        
        Console.WriteLine();
        Console.WriteLine("    RAISE NOTICE 'Attendance template created with ID: %', template_id;");
        Console.WriteLine("END $$;");
        Console.WriteLine();
        
        // Summary
        Console.WriteLine("-- Summary của 28 rewards:");
        for (int i = 0; i < items.Count && i < 28; i++)
        {
            var item = items[i];
            Console.WriteLine($"-- Day {i + 1:D2}: ItemId {item.itemId} (0x{item.itemId:X8}) x{item.amount}");
        }
    }
    
    public static byte[] HexStringToByteArray(string hex)
    {
        int length = hex.Length;
        byte[] data = new byte[length / 2];
        for (int i = 0; i < length; i += 2)
        {
            data[i / 2] = Convert.ToByte(hex.Substring(i, 2), 16);
        }
        return data;
    }
}