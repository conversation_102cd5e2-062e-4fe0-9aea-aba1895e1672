﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroYulgang.Database.FreeSql.Entities.BBG {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class order_detail {

		[JsonProperty, Column(IsPrimary = true, IsIdentity = true, InsertValueSql = "nextval('order_detail_id_seq'::regclass)")]
		public int id { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string status { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string message { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string buyyer { get; set; }

		[JsonProperty]
		public int? amount { get; set; }

		[JsonProperty]
		public long? price { get; set; }

		[JsonProperty]
		public int? marketplace_id { get; set; }

		[JsonProperty]
		public DateTime? updated_at { get; set; }

		[JsonProperty]
		public DateTime? created_at { get; set; }

	}

}
