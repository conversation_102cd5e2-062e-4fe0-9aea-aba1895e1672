﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroYulgang.Database.FreeSql.Entities.BBG {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class vong_quay_result {

		[JsonProperty, Column(IsPrimary = true, IsIdentity = true, InsertValueSql = "nextval('vong_quay_result_id_seq'::regclass)")]
		public long id { get; set; }

		[JsonProperty]
		public int? fld_pid { get; set; }

		[JsonProperty]
		public int? fld_user_id { get; set; }

		[JsonProperty]
		public int? fld_status { get; set; }

		[JsonProperty]
		public DateTime? created_at { get; set; }

		[JsonProperty]
		public DateTime? updated_at { get; set; }

	}

}
