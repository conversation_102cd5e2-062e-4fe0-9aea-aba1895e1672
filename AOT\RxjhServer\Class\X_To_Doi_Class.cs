using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Timers;
using HeroYulgang.Helpers;
using HeroYulgang.Services;
using RxjhServer.HelperTools;

namespace RxjhServer;

public class PartyClass : IDisposable
{
	public ConcurrentDictionary<int, Players> PlayerList;

	public ConcurrentDictionary<string, X_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>ye<PERSON>> PlayerOfflineList;

	public int TotalMember => PlayerList.Count + PlayerOfflineList.Count;

	public string CaptainName;

	public int PartyID;

	public Players InvitedPlayer;

	public Players Captain;

	public int PartyLevel;

	public bool RedPackage;

	public int RedPackageThoiGian;

	public int RuleDistribution;

	public int PhanBoHienTai;

	public Timer AutoPartyEvent;

	private int AutoPartyCount;
	
	public PartyClass(Players Captain)
	{
		AutoPartyEvent = new(5000.0);
		AutoPartyEvent.Elapsed += AutomaticDisplayEvent;
		AutoPartyEvent.AutoReset = true;
		CaptainName = Captain.CharacterName;
		this.Captain = Captain;
		PlayerList = new();
		PlayerList.TryAdd(Captain.SessionID, Captain);
		PhanBoHienTai = 0;
		RuleDistribution = 1;
		RedPackage = false;
		RedPackageThoiGian = 0;
		PartyLevel = Captain.Player_Level;
		PlayerOfflineList = new();
	}

    public Players ThanhVien_DatDuoc_TuongUng(int key)
	{
		try
		{
			var num = 0;
			foreach (var value in PlayerList.Values)
			{
				if (key == num)
				{
					return value;
				}
				num++;
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Nhận tương ứng party NguoiChoi error!" + ex.Message);
		}
		return null;
	}

	public void Dispose()
	{
		try
		{
			PlayerOfflineList?.Clear();
			World.WToDoi.TryRemove(PartyID, out _);
			if (PlayerList != null)
			{
				foreach (var value2 in PlayerList.Values)
				{
					value2.SendPartyDisbandMessage();
					value2.TeamID = 0;
					value2.TeamingStage = 0;
					value2.CoupleInTeam = false;
				}
			}
			PlayerList?.Clear();
			if (AutoPartyEvent != null)
			{
				AutoPartyEvent.Enabled = false;
				AutoPartyEvent.Close();
				AutoPartyEvent.Dispose();
				AutoPartyEvent = null;
			}
			InvitedPlayer = null;
			PartyLevel = 0;
			RedPackage = false;
			RedPackageThoiGian = 0;
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "ToDoi tốt bụng Dispose error!" + ex.Message);
		}
		finally
		{

			PlayerList?.Clear();
			PlayerOfflineList?.Clear();
			World.WToDoi.TryRemove(PartyID, out _);
			if (AutoPartyEvent != null)
			{
				AutoPartyEvent.Enabled = false;
				AutoPartyEvent.Close();
				AutoPartyEvent.Dispose();
				AutoPartyEvent = null;
			}
			InvitedPlayer = null;
			PartyLevel = 0;
			RedPackage = false;
			RedPackageThoiGian = 0;
		}
	}

	private void AutomaticDisplayEvent(object sender, ElapsedEventArgs e)
	{
		var num = 0;
		try
		{
			if (PlayerList != null && TotalMember < 2)
			{
				Dispose();
				return;
			}
			AutoPartyCount++;
			if (AutoPartyCount >= 15)
			{
				AutoPartyCount = 0;
				if (PlayerOfflineList != null && PlayerOfflineList.Count > 0)
				{
					List<string> list = new();
					foreach (var value2 in PlayerOfflineList.Values)
					{
						var players = World.KiemTra_Ten_NguoiChoi(value2.CharacterName);
						if (players == null)
						{
							continue;
						}
						if (value2.TeamID == PartyID)
						{
							if (TotalMember >= World.Gioi_han_so_nguoi_vao_party)
							{
								continue;
							}
							if (Captain.FindPlayers(1000, players))
							{
								if (players.TeamingStage == 0 && TotalMember < World.Gioi_han_so_nguoi_vao_party && players.TeamID == 0)
								{
									var array = Converter.HexStringToByte("AA5528002C0130000600010001002D010000000000000000000000000000000000000000000000000000000055AA");
									System.Buffer.BlockCopy(BitConverter.GetBytes(Captain.SessionID), 0, array, 4, 2);
									System.Buffer.BlockCopy(BitConverter.GetBytes(players.SessionID), 0, array, 14, 2);
									Captain.SendRequestTeam(array, array.Length);
								}
								else if (players.TeamingStage == 1)
								{
									var array2 = Converter.HexStringToByte("AA5512002C013200040001002C01000000000000000055AA");
									System.Buffer.BlockCopy(BitConverter.GetBytes(Captain.SessionID), 0, array2, 4, 2);
									System.Buffer.BlockCopy(BitConverter.GetBytes(Captain.SessionID), 0, array2, 12, 2);
									Captain.AbortTeamRequest(array2, array2.Length);
								}
								else if (players.TeamingStage == 2 && !list.Contains(players.AccountID))
								{
									list.Add(players.AccountID);
								}
							}
							else
							{
								players.HeThongNhacNho("Hiệp khách lạc bước quá xa tổ đội, đồng môn của ngươi đang ở [" + X_Toa_Do_Class.getmapname(Captain.MapID) + "] - tọa độ: [" + Captain.PosX + "," + Captain.PosY + "]!", 10, "Thiên cơ các");
							}
						}
						else if (!list.Contains(players.AccountID))
						{
							list.Add(players.AccountID);
						}
					}
					if (list.Count > 0)
					{
						foreach (var item in list)
						{
							if (PlayerOfflineList.ContainsKey(item))
							{
								PlayerOfflineList.TryRemove(item, out _);
							}
						}
					}
					list.Clear();
				}
			}
			if (RedPackage)
			{
				RedPackageThoiGian -= 3000;
				if (RedPackageThoiGian <= 0)
				{
					RedPackage = false;
					RedPackageThoiGian = 0;
				}
			}
			else
			{
				RedPackage = false;
				RedPackageThoiGian = 0;
			}
			Players value;
			var playersToRemove = new List<Players>(); // Local variable to replace 'tem'

			if (PlayerList != null)
			{
				foreach (var value3 in PlayerList.Values)
				{
					if (World.allConnectedChars.TryGetValue(value3.SessionID, out value))
					{
						value3.ShowPlayers();
						if (RedPackage && RedPackageThoiGian > 0)
						{
							if (value3.AppendStatusList != null && !value3.GetAddState(**********))
							{
								StatusEffect x_Them_Vao_Trang_Thai_Loai = new(value3, RedPackageThoiGian, **********, 0);
								value3.AppendStatusList.Add(x_Them_Vao_Trang_Thai_Loai.FLD_PID, x_Them_Vao_Trang_Thai_Loai);
								value3.StatusEffect(BitConverter.GetBytes(**********), 1, RedPackageThoiGian);
							}
						}
						else if (value3.AppendStatusList != null && value3.GetAddState(**********))
						{
							value3.AppendStatusList[**********].ThoiGianKetThucSuKien();
						}
					}
					else if (!playersToRemove.Contains(value3))
					{
						playersToRemove.Add(value3);
					}
				}
			}

			// Process players to remove
			foreach (var playerToRemove in playersToRemove)
			{
				if (PlayerList != null && PlayerList.TryGetValue(playerToRemove.SessionID, out value))
				{
					PlayerList.TryRemove(playerToRemove.SessionID, out _);
					playerToRemove.TeamID = 0;
					playerToRemove.TeamingStage = 0;
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Auto Party tạo nhóm Tổ Đội bị lỗi - [" + num + "] - [" + ex.Message);
		}
	}

	public void UyQuyen_DoiTruong(Players Old_DoiTruong, Players New_DoiTruong)
	{
		try
		{
			CaptainName = New_DoiTruong.CharacterName;
			Captain = New_DoiTruong;
			foreach (var value in PlayerList.Values)
			{
				value.UyQuyen_DoiTruong_NhacNho(Old_DoiTruong, New_DoiTruong);
				value.ShowPlayers();
			}

		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Chuyển quyền Key đội trưởng Lỗi !! - " + ex.Message);
		}
	}

	public void ThamGiaThanhVienNhom_NhacNho(Players NguoiChoi)
	{
		try
		{
			if (NguoiChoi.FLD_Couple.Length != 0)
			{
				foreach (var value2 in PlayerList.Values)
				{
					if (value2.CharacterName == NguoiChoi.FLD_Couple)
					{
						NguoiChoi.CoupleInTeam = true;
						value2.CoupleInTeam = true;
						break;
					}
				}
			}
			foreach (var value3 in PlayerList.Values)
			{
				if (NguoiChoi != value3)
				{
					value3.GiaNhap_ToDoi_NhacNho(NguoiChoi);
					NguoiChoi.GiaNhap_ToDoi_NhacNho(value3);
				}
				value3.ShowPlayers();
			}
			if (TotalMember >= 2)
			{
				AutoPartyEvent.Enabled = true;
			}
			if (PlayerOfflineList.TryGetValue(NguoiChoi.AccountID, out var _))
			{
				PlayerOfflineList.TryRemove(NguoiChoi.AccountID, out _);
			}

		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "ToDoi tốt bụng ThamGiaThanhVienNhom_NhacNho error!" + ex.Message);
		}
	}

	public void LeaveParty(Players NguoiChoi, int Exit_ID)
	{
		var num = 0;
		try
		{
			// type 2 => bị kick
			if (PlayerList != null && PlayerList.ContainsKey(NguoiChoi.SessionID))
			{
				PlayerList.TryRemove(NguoiChoi.SessionID, out _);
				// Leave type 1 => bị ngắt kết nối
				if (Exit_ID == 1 && TotalMember >= 2 && !PlayerOfflineList.ContainsKey(NguoiChoi.AccountID))
				{
					PlayerOfflineList.TryAdd(NguoiChoi.AccountID, new()
					{
						TeamID = NguoiChoi.TeamID,
						CharacterName = NguoiChoi.CharacterName,
						Job = NguoiChoi.Player_Job,
						JobLevel = NguoiChoi.Player_Level,
						Level = NguoiChoi.Player_Level
					});
				}
			}
			if (NguoiChoi.GetAddState(**********))
			{
				NguoiChoi.AppendStatusList[**********].ThoiGianKetThucSuKien();
			}
			num = 1;
			if (NguoiChoi.FLD_Couple.Length != 0)
			{
				NguoiChoi.CoupleInTeam = false;
				num = 3;
				if (PlayerList != null)
				{
					foreach (var value in PlayerList.Values)
					{
						num = 4;
						if (value.CharacterName == NguoiChoi.FLD_Couple)
						{
							num = 5;
							value.CoupleInTeam = false;
							num = 6;
							break;
						}
					}
				}
			}
			num = 7;
			if (PlayerList != null && TotalMember >= 2)
			{
				num = 8;
				if (CaptainName!= NguoiChoi.CharacterName)
				{
					foreach (var value2 in PlayerList.Values)
					{
						num = 9;
						value2.RoiKhoi_ToDoi_NhacNho(NguoiChoi);
						num = 10;
						value2.ShowPlayers();
						num = 11;
					}
				}
				else
				{
					var flag = true;
					foreach (var value3 in PlayerList.Values)
					{
						if (flag)
						{
							num = 12;
							UyQuyen_DoiTruong(NguoiChoi, value3);
							flag = false;
						}
						num = 15;
						value3.HeThongNhacNho("Đội trưởng được chuyển cho " + CaptainName + "!", 8, "Thiên Cơ Lệnh");
						value3.RoiKhoi_ToDoi_NhacNho(NguoiChoi);
						value3.ShowPlayers();
						num = 16;
					}
				}
			}
			else
			{
				num = 17;
				Dispose();
			}
			num = 18;
			NguoiChoi.this_RoiKhoi_ToDoi_NhacNho();
			num = 19;
			NguoiChoi.TeamID = 0;
			num = 20;
			NguoiChoi.TeamingStage = 0;
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Tổ Đội Party Thoát bị error! |" + num + "|" + ex.Message);
		}
		finally
		{
			NguoiChoi.TeamID = 0;
			NguoiChoi.TeamingStage = 0;
		}
	}

}
