using System;
using System.Collections.Generic;
using System.Linq;
using HeroYulgang.Helpers;

namespace RxjhServer.TestBot.Core
{
    /// <summary>
    /// Core controller cho Test Bot system
    /// </summary>
    public class TestBotCore : IBotCore
    {
        private readonly Dictionary<string, IBotModule> _modules = new Dictionary<string, IBotModule>();
        private readonly object _lockObject = new object();
        private bool _disposed = false;
        
        public Players Player { get; private set; }
        public BotConfig Config { get; private set; }
        public bool IsRunning { get; private set; }
        
        public event Action<IBotCore> OnBotStarted;
        public event Action<IBotCore> OnBotStopped;
        public event Action<IBotCore, Exception> OnError;
        
        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="player">Player instance</param>
        /// <param name="config">Bot configuration</param>
        public TestBotCore(Players player, BotConfig config = null)
        {
            Player = player ?? throw new ArgumentNullException(nameof(player));
            Config = config ?? BotConfig.CreateDefault(player);
            
            LogDebug($"TestBotCore initialized for player {Player.CharacterName}");
        }
        
        /// <summary>
        /// Đăng ký module
        /// </summary>
        /// <param name="module">Module cần đăng ký</param>
        public void RegisterModule(IBotModule module)
        {
            if (module == null)
                throw new ArgumentNullException(nameof(module));
                
            lock (_lockObject)
            {
                if (_modules.ContainsKey(module.ModuleName))
                {
                    LogWarning($"Module {module.ModuleName} already registered, replacing...");
                    _modules[module.ModuleName].Cleanup();
                    _modules[module.ModuleName].Dispose();
                }
                
                _modules[module.ModuleName] = module;
                
                try
                {
                    module.Initialize(Player, Config);
                    LogDebug($"Module {module.ModuleName} registered and initialized");
                }
                catch (Exception ex)
                {
                    LogError($"Failed to initialize module {module.ModuleName}: {ex.Message}");
                    _modules.Remove(module.ModuleName);
                    OnError?.Invoke(this, ex);
                }
            }
        }
        
        /// <summary>
        /// Hủy đăng ký module
        /// </summary>
        /// <param name="moduleName">Tên module cần hủy</param>
        public void UnregisterModule(string moduleName)
        {
            if (string.IsNullOrEmpty(moduleName))
                return;
                
            lock (_lockObject)
            {
                if (_modules.TryGetValue(moduleName, out var module))
                {
                    try
                    {
                        module.Cleanup();
                        module.Dispose();
                        _modules.Remove(moduleName);
                        LogDebug($"Module {moduleName} unregistered");
                    }
                    catch (Exception ex)
                    {
                        LogError($"Error unregistering module {moduleName}: {ex.Message}");
                        OnError?.Invoke(this, ex);
                    }
                }
            }
        }
        
        /// <summary>
        /// Lấy module theo tên
        /// </summary>
        /// <typeparam name="T">Loại module</typeparam>
        /// <param name="moduleName">Tên module</param>
        /// <returns>Module instance hoặc null</returns>
        public T GetModule<T>(string moduleName) where T : class, IBotModule
        {
            if (string.IsNullOrEmpty(moduleName))
                return null;
                
            lock (_lockObject)
            {
                return _modules.TryGetValue(moduleName, out var module) ? module as T : null;
            }
        }
        
        /// <summary>
        /// Bắt đầu bot
        /// </summary>
        public void Start()
        {
            if (IsRunning)
                return;
                
            try
            {
                IsRunning = true;
                LogInfo($"TestBot started for player {Player.CharacterName}");
                OnBotStarted?.Invoke(this);
            }
            catch (Exception ex)
            {
                LogError($"Error starting bot: {ex.Message}");
                IsRunning = false;
                OnError?.Invoke(this, ex);
            }
        }
        
        /// <summary>
        /// Dừng bot
        /// </summary>
        public void Stop()
        {
            if (!IsRunning)
                return;
                
            try
            {
                IsRunning = false;
                LogInfo($"TestBot stopped for player {Player.CharacterName}");
                OnBotStopped?.Invoke(this);
            }
            catch (Exception ex)
            {
                LogError($"Error stopping bot: {ex.Message}");
                OnError?.Invoke(this, ex);
            }
        }
        
        /// <summary>
        /// Cập nhật bot (main loop)
        /// </summary>
        public void Update()
        {
            if (!IsRunning || !Config.IsEnabled)
                return;
                
            try
            {
                // Kiểm tra player còn valid không
                if (Player == null || Player.PlayerTuVong)
                    return;
                    
                // Cập nhật các module theo thứ tự ưu tiên
                var sortedModules = GetSortedModules();
                
                foreach (var module in sortedModules)
                {
                    if (!module.IsEnabled || !module.CanExecute())
                        continue;
                        
                    try
                    {
                        module.Update();
                    }
                    catch (Exception ex)
                    {
                        LogError($"Error updating module {module.ModuleName}: {ex.Message}");
                        OnError?.Invoke(this, ex);
                    }
                }
            }
            catch (Exception ex)
            {
                LogError($"Error in bot update loop: {ex.Message}");
                OnError?.Invoke(this, ex);
            }
        }
        
        /// <summary>
        /// Lấy danh sách module đã sắp xếp theo priority
        /// </summary>
        /// <returns>Danh sách module đã sắp xếp</returns>
        private List<IBotModule> GetSortedModules()
        {
            lock (_lockObject)
            {
                return _modules.Values.OrderBy(m => m.Priority).ToList();
            }
        }
        
        /// <summary>
        /// Dispose resources
        /// </summary>
        public void Dispose()
        {
            if (_disposed)
                return;
                
            try
            {
                Stop();
                
                lock (_lockObject)
                {
                    foreach (var module in _modules.Values)
                    {
                        try
                        {
                            module.Cleanup();
                            module.Dispose();
                        }
                        catch (Exception ex)
                        {
                            LogError($"Error disposing module {module.ModuleName}: {ex.Message}");
                        }
                    }
                    _modules.Clear();
                }
                
                _disposed = true;
                LogDebug($"TestBotCore disposed for player {Player?.CharacterName}");
            }
            catch (Exception ex)
            {
                LogError($"Error disposing TestBotCore: {ex.Message}");
            }
        }
        
        #region Logging Methods
        
        private void LogDebug(string message)
        {
            if (Config.DebugMode)
            {
                LogHelper.WriteLine(LogLevel.Debug, $"[TestBot] {message}");
            }
        }
        
        private void LogInfo(string message)
        {
            LogHelper.WriteLine(LogLevel.Info, $"[TestBot] {message}");
        }
        
        private void LogWarning(string message)
        {
            LogHelper.WriteLine(LogLevel.Warning, $"[TestBot] {message}");
        }
        
        private void LogError(string message)
        {
            LogHelper.WriteLine(LogLevel.Error, $"[TestBot] {message}");
        }
        
        #endregion
    }
}
