﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroYulgang.Database.FreeSql.Entities.Public {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class tbl_xwwl_sell {

		[JsonProperty, Column(IsIdentity = true, InsertValueSql = "nextval('tbl_xwwl_sell_id_seq'::regclass)")]
		public int id { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_npcname { get; set; }

		[JsonProperty]
		public long? fld_nid { get; set; }

		[JsonProperty]
		public int? fld_index { get; set; }

		[JsonProperty]
		public long? fld_pid { get; set; }

		[JsonProperty]
		public long? fld_money { get; set; }

		[JsonProperty]
		public int? fld_magic0 { get; set; }

		[JsonProperty]
		public int? fld_magic1 { get; set; }

		[JsonProperty]
		public int? fld_magic2 { get; set; }

		[JsonProperty]
		public int? fld_magic3 { get; set; }

		[JsonProperty]
		public int? fld_magic4 { get; set; }

		[JsonProperty]
		public int? fld_canvohuan { get; set; }

		[JsonProperty]
		public int? fld_days { get; set; }

		[JsonProperty]
		public int? fld_bd { get; set; }

	}

}
