using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using System.Linq;
using HeroYulgang.Helpers;
using RxjhServer.AOI;
using static RxjhServer.ReverserInfo;

namespace RxjhServer.NpcManager
{
    /// <summary>
    /// Represents the movement state of an NPC for interpolation
    /// </summary>
    public class NPCMovementState
    {
        public float StartX { get; set; }
        public float StartY { get; set; }
        public float TargetX { get; set; }
        public float TargetY { get; set; }
        public DateTime StartTime { get; set; }
        public float Speed { get; set; } // pixels per second
        public bool IsMoving { get; set; }
        public NPCBehaviorType MovementType { get; set; }
        public NpcClass NpcReference { get; set; } // Direct reference to NPC

        public NPCMovementState()
        {
            IsMoving = false;
            Speed = 50f;
            MovementType = NPCBehaviorType.Idle;
        }
    }

    /// <summary>
    /// Represents the movement target coordinates for an NPC
    /// </summary>
    public class NPCMovementTarget
    {
        public float TargetX { get; set; }
        public float TargetY { get; set; }
        public float MovementSpeed { get; set; }
        public DateTime MovementStartTime { get; set; }
        public bool IsMoving { get; set; }
        public Players TargetPlayer { get; set; } // For attacking behavior

        public NPCMovementTarget()
        {
            TargetX = 0;
            TargetY = 0;
            MovementSpeed = 10f;
            MovementStartTime = DateTime.Now;
            IsMoving = false;
            TargetPlayer = null;
        }

        public NPCMovementTarget(float targetX, float targetY, float speed)
        {
            TargetX = targetX;
            TargetY = targetY;
            MovementSpeed = speed;
            MovementStartTime = DateTime.Now;
            IsMoving = true;
            TargetPlayer = null;
        }
    }

    /// <summary>
    /// Represents the behavior state of an NPC to prevent rapid behavior switching
    /// </summary>
    public class NPCBehaviorState
    {
        public NPCBehaviorType CurrentBehavior { get; set; }
        public DateTime BehaviorStartTime { get; set; }
        public int MinimumDuration { get; set; } // Minimum duration in milliseconds
        public Players LastTarget { get; set; }
        public bool IsLocked { get; set; } // Prevents behavior switching when true
        public DateTime LastBehaviorChange { get; set; }
        public NPCMovementTarget MovementTarget { get; set; } // Target coordinates for movement
        public DateTime LastAttackTime { get; set; } = DateTime.MinValue; // For attack speed control

        public NPCBehaviorState()
        {
            CurrentBehavior = NPCBehaviorType.Idle;
            BehaviorStartTime = DateTime.Now;
            MinimumDuration = 1000; // Default 1 second minimum
            LastTarget = null;
            IsLocked = false;
            LastBehaviorChange = DateTime.Now;
            MovementTarget = new NPCMovementTarget();
        }

        /// <summary>
        /// Check if enough time has passed to allow behavior change
        /// </summary>
        public bool CanChangeBehavior()
        {
            var elapsed = (DateTime.Now - BehaviorStartTime).TotalMilliseconds;
            return !IsLocked && elapsed >= MinimumDuration;
        }

        /// <summary>
        /// Check if enough time has passed to allow attack
        /// </summary>
        public bool CanAttack(int attackSpeedMs = -1) // Use default attack speed if not specified
        {
            if (attackSpeedMs == -1)
                attackSpeedMs = NPCBehaviorManager.ATTACK_SPEED_MS;
            return (DateTime.Now - LastAttackTime).TotalMilliseconds >= attackSpeedMs;
        }

        /// <summary>
        /// Update behavior state with new behavior
        /// </summary>
        public void UpdateBehavior(NPCBehaviorType newBehavior, Players target = null)
        {
            if (CurrentBehavior != newBehavior)
            {
                CurrentBehavior = newBehavior;
                BehaviorStartTime = DateTime.Now;
                LastBehaviorChange = DateTime.Now;
                LastTarget = target;

                // Set minimum duration based on behavior type - New 4-state system
                MinimumDuration = newBehavior switch
                {
                    NPCBehaviorType.Idle => 10000,       // 2 seconds minimum idle (standing still)
                    NPCBehaviorType.Moving => 15000,     // 3 seconds minimum moving (random movement)
                    NPCBehaviorType.Attacking => 1500,  // 1.5 seconds minimum attacking
                    NPCBehaviorType.Returning => 2000,  // 2 seconds minimum returning to spawn
                    _ => 1000                            // Default 1 second minimum
                };
            }
        }
    }

    /// <summary>
    /// Manages NPC behavior including movement, chase, attack, and return to spawn logic
    /// </summary>
    public static class NPCBehaviorManager
    {
        // Distance constants - now modifiable
        public static int ATTACK_RANGE = 10;           // Tầm đánh
        public static int CHASE_RANGE = 80;           // Tầm phát hiện để chase
        public static int ACTIVITY_AREA = 120;         // Vùng hoạt động từ spawn point
        public static int RETURN_TO_SPAWN_RANGE = 160; // Khoảng cách để return về spawn

        // Attack speed control
        public static int ATTACK_SPEED_MS = 2500;      // Khoảng cách giữa 2 lần tấn công (milliseconds)

        // Movement throttling - now modifiable and thread-safe
        private static readonly ConcurrentDictionary<int, DateTime> _lastMovementTime = new ConcurrentDictionary<int, DateTime>();
        private static readonly ConcurrentDictionary<int, DateTime> _lastPositionUpdate = new ConcurrentDictionary<int, DateTime>();

        public static int MOVEMENT_THROTTLE_MS = 100;  // Tối thiểu 1 giây giữa các movement
        public static int POSITION_UPDATE_THROTTLE_MS = 100; // Tối thiểu 0.5 giây giữa các position update

        // Movement interpolation system
        private static readonly ConcurrentDictionary<int, NPCMovementState> _npcMovementStates = new();
        private static readonly Timer _movementUpdateTimer;

        // Behavior state persistence system
        private static readonly ConcurrentDictionary<int, NPCBehaviorState> _npcBehaviorStates = new();

        // Movement speeds (pixels per second) - redesigned for new 4-state system
        public static float IDLE_MOVEMENT_SPEED = 0f;      // Idle = standing still (no movement)
        public static float MOVING_SPEED = 30f;            // Random movement speed
        public static float ATTACKING_SPEED = 200f;         // Fast speed when chasing/attacking
        public static float RETURNING_SPEED = 10f;         // Speed when returning to spawn

        static NPCBehaviorManager()
        {
            // Initialize movement update timer - runs every 100ms
            _movementUpdateTimer = new Timer(UpdateAllNPCMovements, null, 100, 100);
        }

        /// <summary>
        /// Update all NPC movements - called by timer every 100ms
        /// </summary>
        private static void UpdateAllNPCMovements(object state)
        {
            try
            {
                var now = DateTime.Now;
                var npcsToRemove = new List<int>();

                foreach (var kvp in _npcMovementStates)
                {
                    var npcSessionId = kvp.Key;
                    var movementState = kvp.Value;

                    if (!movementState.IsMoving)
                        continue;

                    // Get the NPC from movement state
                    var npc = movementState.NpcReference;
                    if (npc == null || npc.NPCDeath)
                    {
                        npcsToRemove.Add(npcSessionId);
                        continue;
                    }

                    // Calculate movement progress
                    var elapsedTime = (float)(now - movementState.StartTime).TotalSeconds;
                    var totalDistance = CalculateDistance(movementState.StartX, movementState.StartY,
                                                        movementState.TargetX, movementState.TargetY);
                    var travelTime = totalDistance / movementState.Speed;

                    if (elapsedTime >= travelTime)
                    {
                        // Movement completed
                        npc.Rxjh_X = movementState.TargetX;
                        npc.Rxjh_Y = movementState.TargetY;
                        movementState.IsMoving = false;

                        // Update AOI position
                        try
                        {
                            if (AOIConfiguration.Instance.ShouldUseAOI(npc.Rxjh_Map))
                            {
                                AOIManager.Instance.UpdateNPCPosition(npc, npc.Rxjh_X, npc.Rxjh_Y);
                            }
                        }
                        catch (Exception ex)
                        {
                            LogHelper.WriteLine(LogLevel.Error, $"Error updating NPC AOI position: {ex.Message}");
                        }
                    }
                    else
                    {
                        // Interpolate position
                        var progress = elapsedTime / travelTime;
                        npc.Rxjh_X = movementState.StartX + (movementState.TargetX - movementState.StartX) * (float)progress;
                        npc.Rxjh_Y = movementState.StartY + (movementState.TargetY - movementState.StartY) * (float)progress;

                        // Update AOI position periodically
                        if (CanUpdatePosition(npcSessionId))
                        {
                            try
                            {
                                if (AOIConfiguration.Instance.ShouldUseAOI(npc.Rxjh_Map))
                                {
                                    AOIManager.Instance.UpdateNPCPosition(npc, npc.Rxjh_X, npc.Rxjh_Y);
                                }
                                _lastPositionUpdate[npcSessionId] = now;
                            }
                            catch (Exception ex)
                            {
                                LogHelper.WriteLine(LogLevel.Error, $"Error updating NPC AOI position during movement: {ex.Message}");
                            }
                        }
                    }
                }

                // Clean up completed movements
                foreach (var npcId in npcsToRemove)
                {
                    _npcMovementStates.TryRemove(npcId, out _);
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error in UpdateAllNPCMovements: {ex.Message}");
            }
        }

        /// <summary>
        /// Start smooth movement for an NPC
        /// </summary>
        public static void StartNPCMovement(NpcClass npc, float targetX, float targetY, float speed, NPCBehaviorType movementType)
        {
            try
            {
                var movementState = _npcMovementStates.GetOrAdd(npc.NPC_SessionID, _ => new NPCMovementState());

                // Set up movement state
                movementState.StartX = npc.Rxjh_X;
                movementState.StartY = npc.Rxjh_Y;
                movementState.TargetX = targetX;
                movementState.TargetY = targetY;
                movementState.StartTime = DateTime.Now;
                movementState.Speed = speed;
                movementState.IsMoving = true;
                movementState.MovementType = movementType;
                movementState.NpcReference = npc;

                // Send movement packet to clients immediately for visual feedback
                npc.SendMovingData(targetX, targetY, 55, 2);
                RecordMovement(npc.NPC_SessionID);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error starting NPC movement for {npc.NPC_SessionID}: {ex.Message}");
            }
        }

        /// <summary>
        /// Stop NPC movement
        /// </summary>
        public static void StopNPCMovement(NpcClass npc)
        {
            try
            {
                if (_npcMovementStates.TryGetValue(npc.NPC_SessionID, out var movementState))
                {
                    movementState.IsMoving = false;
                    LogHelper.WriteLine(LogLevel.Debug, $"Stopped NPC movement: {npc.Name}");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error stopping NPC movement for {npc.NPC_SessionID}: {ex.Message}");
            }
        }

        /// <summary>
        /// Check if NPC is currently moving
        /// </summary>
        public static bool IsNPCMoving(NpcClass npc)
        {
            return _npcMovementStates.TryGetValue(npc.NPC_SessionID, out var movementState) && movementState.IsMoving;
        }

        /// <summary>
        /// Clean up movement state when NPC is removed
        /// </summary>
        public static void CleanupNPCMovement(int npcSessionId)
        {
            try
            {
                _npcMovementStates.TryRemove(npcSessionId, out _);
                _lastMovementTime.TryRemove(npcSessionId, out _);
                _lastPositionUpdate.TryRemove(npcSessionId, out _);
                LogHelper.WriteLine(LogLevel.Debug, $"Cleaned up movement state for NPC {npcSessionId}");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error cleaning up NPC movement for {npcSessionId}: {ex.Message}");
            }
        }

        /// <summary>
        /// Dispose resources when shutting down
        /// </summary>
        public static void Dispose()
        {
            try
            {
                _movementUpdateTimer?.Dispose();
                _npcMovementStates.Clear();
                _lastMovementTime.Clear();
                _lastPositionUpdate.Clear();
                LogHelper.WriteLine(LogLevel.Info, "NPCBehaviorManager disposed successfully");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error disposing NPCBehaviorManager: {ex.Message}");
            }
        }

        /// <summary>
        /// Calculate distance between two points
        /// </summary>
        public static double CalculateDistance(float x1, float y1, float x2, float y2)
        {
            var dx = x1 - x2;
            var dy = y1 - y2;
            return Math.Sqrt(dx * dx + dy * dy);
        }
        
        /// <summary>
        /// Calculate distance from NPC current position to spawn point
        /// </summary>
        public static double GetDistanceFromSpawn(NpcClass npc)
        {
            return CalculateDistance(npc.Rxjh_X, npc.Rxjh_Y, npc.Rxjh_cs_X, npc.Rxjh_cs_Y);
        }
        
        /// <summary>
        /// Check if NPC can move (throttling)
        /// </summary>
        public static bool CanMove(int npcSessionID)
        {
            var now = DateTime.Now;
            if (_lastMovementTime.TryGetValue(npcSessionID, out var lastTime))
            {
                return (now - lastTime).TotalMilliseconds >= MOVEMENT_THROTTLE_MS;
            }
            return true;
        }

        /// <summary>
        /// Get or create behavior state for an NPC
        /// </summary>
        public static NPCBehaviorState GetBehaviorState(int npcSessionID)
        {
            return _npcBehaviorStates.GetOrAdd(npcSessionID, _ => new NPCBehaviorState());
        }

        /// <summary>
        /// Remove behavior state when NPC is removed
        /// </summary>
        public static void RemoveBehaviorState(int npcSessionID)
        {
            _npcBehaviorStates.TryRemove(npcSessionID, out _);
        }

        /// <summary>
        /// Check if NPC can change behavior based on state persistence rules
        /// </summary>
        public static bool CanChangeBehavior(int npcSessionID, NPCBehaviorType newBehavior)
        {
            var behaviorState = GetBehaviorState(npcSessionID);

            // Always allow if it's the same behavior
            if (behaviorState.CurrentBehavior == newBehavior)
                return true;

            // Check if minimum duration has passed
            return behaviorState.CanChangeBehavior();
        }

        /// <summary>
        /// Update NPC behavior state
        /// </summary>
        public static void UpdateBehaviorState(int npcSessionID, NPCBehaviorType newBehavior, Players target = null)
        {
            var behaviorState = GetBehaviorState(npcSessionID);
            behaviorState.UpdateBehavior(newBehavior, target);
        }
        
        /// <summary>
        /// Check if NPC can update position (throttling)
        /// </summary>
        public static bool CanUpdatePosition(int npcSessionID)
        {
            var now = DateTime.Now;
            if (_lastPositionUpdate.TryGetValue(npcSessionID, out var lastTime))
            {
                return (now - lastTime).TotalMilliseconds >= POSITION_UPDATE_THROTTLE_MS;
            }
            return true;
        }
        
        /// <summary>
        /// Record movement time for throttling
        /// </summary>
        public static void RecordMovement(int npcSessionID)
        {
            try
            {
                _lastMovementTime[npcSessionID] = DateTime.Now;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error recording movement time for NPC {npcSessionID}: {ex.Message}");
            }
        }

        /// <summary>
        /// Record position update time for throttling
        /// </summary>
        public static void RecordPositionUpdate(int npcSessionID)
        {
            try
            {
                _lastPositionUpdate[npcSessionID] = DateTime.Now;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error recording position update time for NPC {npcSessionID}: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Determine NPC behavior based on current state and nearby players with state persistence
        /// </summary>
        public static NPCBehaviorType DetermineBehavior(NpcClass npc, Players targetPlayer)
        {
            // Get current behavior state
            var behaviorState = GetBehaviorState(npc.NPC_SessionID);

            // Calculate new desired behavior
            var desiredBehavior = CalculateDesiredBehavior(npc, targetPlayer);

            // Check if we can change to the desired behavior
            if (CanChangeBehavior(npc.NPC_SessionID, desiredBehavior))
            {
                // Update behavior state if changing
                if (behaviorState.CurrentBehavior != desiredBehavior)
                {
                    UpdateBehaviorState(npc.NPC_SessionID, desiredBehavior, targetPlayer);
                }
                return desiredBehavior;
            }

            // Stay in current behavior if change is not allowed
            return behaviorState.CurrentBehavior;
        }

        /// <summary>
        /// Calculate desired behavior without state persistence (internal logic) - New 4-state system
        /// </summary>
         private static NPCBehaviorType CalculateDesiredBehavior(NpcClass npc, Players targetPlayer)
        {
            // NPC is dead or invalid
            if (npc.NPCDeath || npc.FLD_AT <= 0)
                return NPCBehaviorType.Idle;

            var distanceFromSpawn = GetDistanceFromSpawn(npc);
            var currentState = GetBehaviorState(npc.NPC_SessionID);

            // Priority 1: Return to spawn if too far away (unless currently attacking)
            if (distanceFromSpawn > RETURN_TO_SPAWN_RANGE)
            {
                // If currently attacking, allow some extra distance before returning
                if (currentState?.CurrentBehavior == NPCBehaviorType.Attacking && distanceFromSpawn <= RETURN_TO_SPAWN_RANGE * 1.5)
                {
                    // Continue attacking if target is still valid
                    if (targetPlayer != null  && targetPlayer.NhanVat_HP > 0 && !targetPlayer.PlayerTuVong && !targetPlayer.Exiting)
                    {
                        return NPCBehaviorType.Attacking;
                    }
                }
                return NPCBehaviorType.Returning;
            }

            // Priority 2: Continue attacking current target if valid
            if (currentState?.CurrentBehavior == NPCBehaviorType.Attacking &&
                currentState.MovementTarget?.TargetPlayer != null )
            {
                var currentTarget = currentState.MovementTarget.TargetPlayer;

                // Check if current target is still valid
                if (currentTarget.GMMode != 8 && currentTarget.NhanVat_HP > 0 && !currentTarget.PlayerTuVong && !currentTarget.Exiting)
                {
                    var distanceToCurrentTarget = CalculateDistance(npc.Rxjh_X, npc.Rxjh_Y, currentTarget.PosX, currentTarget.PosY);

                    // Continue attacking if target is within extended chase range
                    if (distanceToCurrentTarget <= CHASE_RANGE * 1.5) // Allow some extra chase distance
                    {
                        return NPCBehaviorType.Attacking;
                    }
                }
            }

            // Priority 3: Check for new target to attack
            if (targetPlayer != null && targetPlayer.GMMode != 8 && targetPlayer.NhanVat_HP > 0 && !targetPlayer.PlayerTuVong)
            {
                var distanceToPlayer = CalculateDistance(npc.Rxjh_X, npc.Rxjh_Y, targetPlayer.PosX, targetPlayer.PosY);

                // For aggressive NPCs (FLD_AUTO = 1) OR passive NPCs that have been activated (have players in PlayList)
                if (npc.FLD_AUTO == 1 || (npc.FLD_AUTO == 0 && npc.GetNearbyPlayersCount() > 0))
                {
                    // Player is within chase range and NPC is within activity area
                    if (distanceToPlayer <= CHASE_RANGE && distanceFromSpawn <= ACTIVITY_AREA)
                    {
                        return NPCBehaviorType.Attacking;
                    }
                }
            }

            // Priority 4: Get current behavior state to decide between Idle and Moving
            if (currentState != null)
            {
                // If currently idle and enough time has passed, switch to moving
                if (currentState.CurrentBehavior == NPCBehaviorType.Idle &&
                    (DateTime.Now - currentState.BehaviorStartTime).TotalMilliseconds > 2000)
                {
                    return NPCBehaviorType.Moving;
                }
                // If currently moving and enough time has passed, switch to idle
                else if (currentState.CurrentBehavior == NPCBehaviorType.Moving &&
                         (DateTime.Now - currentState.BehaviorStartTime).TotalMilliseconds > 3000)
                {
                    return NPCBehaviorType.Idle;
                }
            }

            // Priority 5: Default to current behavior or Idle if no current state
            return currentState?.CurrentBehavior ?? NPCBehaviorType.Idle;
        }
        
        /// <summary>
        /// Execute NPC behavior
        /// </summary>
        public static void ExecuteBehavior(NpcClass npc, NPCBehaviorType behavior, Players targetPlayer = null)
        {

            // Ensure NPC states are properly set before executing behavior
            //EnsureNPCStateConsistency(npc, behavior);

            switch (behavior)
            {
                case NPCBehaviorType.Idle:
                    ExecuteIdleBehavior(npc);
                    break;

                case NPCBehaviorType.Moving:
                    ExecuteMovingBehavior(npc);
                    break;

                case NPCBehaviorType.Attacking:
                    if (targetPlayer != null && !targetPlayer.Exiting)
                        ExecuteAttackingBehavior(npc, targetPlayer);
                    else
                    {
                        LogHelper.WriteLine(LogLevel.Debug, $"NPC {npc.NPC_SessionID} has no valid target, falling back to idle");
                        UpdateBehaviorState(npc.NPC_SessionID, NPCBehaviorType.Idle, null);
                        ExecuteIdleBehavior(npc); // Fallback to idle if no target
                    }
                    break;

                case NPCBehaviorType.Returning:
                    ExecuteReturningBehavior(npc);
                    break;

                default:
                    ExecuteIdleBehavior(npc);
                    break;
            }
        }
        
        /// <summary>
        /// Execute idle behavior - NPC stands still (no movement)
        /// </summary>
        private static void ExecuteIdleBehavior(NpcClass npc)
        {
            // Idle means standing still - stop all movement and attacks
            npc.AutomaticMoveEnabled = false;
            npc.AutomaticAttackEnabled = false;

            // Clear any movement target
            var behaviorState = GetBehaviorState(npc.NPC_SessionID);
            if (behaviorState?.MovementTarget != null)
            {
                behaviorState.MovementTarget.IsMoving = false;
                behaviorState.MovementTarget.TargetPlayer = null;
            }

            // Clear any stale target data
            npc.PlayCw = null;
        }

        /// <summary>
        /// Execute moving behavior - NPC moves randomly around spawn area
        /// </summary>
        private static void ExecuteMovingBehavior(NpcClass npc)
        {
            var behaviorState = GetBehaviorState(npc.NPC_SessionID);
            if (behaviorState?.MovementTarget == null || !behaviorState.MovementTarget.IsMoving)
            {
                // Initialize movement target if not set
                var moveRange = 50; // Random movement range
                var random = new Random(npc.NPC_SessionID + DateTime.Now.Millisecond);
                var angle = random.NextDouble() * Math.PI * 2; // Full 360 degrees

                var targetX = npc.Rxjh_cs_X + (float)(Math.Cos(angle) * moveRange);
                var targetY = npc.Rxjh_cs_Y + (float)(Math.Sin(angle) * moveRange);

                // Set movement target
                behaviorState.MovementTarget = new NPCMovementTarget(targetX, targetY, MOVING_SPEED);
            }

            // Enable movement, disable attack
            npc.AutomaticMoveEnabled = true;
            npc.AutomaticAttackEnabled = false;
            npc.PlayCw = null;
        }

        /// <summary>
        /// Execute attacking behavior - NPC chases and attacks player
        /// </summary>
        public static void ExecuteAttackingBehavior(NpcClass npc, Players targetPlayer)
        {
            var distanceToPlayer = CalculateDistance(npc.Rxjh_X, npc.Rxjh_Y, targetPlayer.PosX, targetPlayer.PosY);
            var behaviorState = GetBehaviorState(npc.NPC_SessionID);

            if (distanceToPlayer <= ATTACK_RANGE)
            {
                // Close enough to attack - stop moving and attack
                npc.AutomaticMoveEnabled = false;
                npc.AutomaticAttackEnabled = true;

                if (behaviorState?.MovementTarget != null)
                {
                    behaviorState.MovementTarget.IsMoving = false;
                }

                // Check attack speed before attacking
                if (behaviorState != null && behaviorState.CanAttack()) // Use default attack speed
                {
                    npc.ProcessAutomaticAttack();
                    behaviorState.LastAttackTime = DateTime.Now; // Update last attack time
                }
            }
            else
            {
                // Too far - chase to attack range (not player position)
                var (attackX, attackY) = CalculateOptimalAttackPosition(npc, targetPlayer);

                if (behaviorState?.MovementTarget != null)
                {
                    behaviorState.MovementTarget.TargetX = attackX;
                    behaviorState.MovementTarget.TargetY = attackY;
                    behaviorState.MovementTarget.MovementSpeed = ATTACKING_SPEED;
                    behaviorState.MovementTarget.IsMoving = true;
                    behaviorState.MovementTarget.TargetPlayer = targetPlayer;
                }
                npc.SendMovingData(attackX, attackY, 55, 2);

                npc.AutomaticMoveEnabled = true;
                npc.AutomaticAttackEnabled = true;
            }
        }

        /// <summary>
        /// Execute returning behavior - NPC returns to spawn point
        /// </summary>
        private static void ExecuteReturningBehavior(NpcClass npc)
        {
            var behaviorState = GetBehaviorState(npc.NPC_SessionID);
            if (behaviorState?.MovementTarget != null)
            {
                behaviorState.MovementTarget.TargetX = npc.Rxjh_cs_X;
                behaviorState.MovementTarget.TargetY = npc.Rxjh_cs_Y;
                behaviorState.MovementTarget.MovementSpeed = RETURNING_SPEED;
                behaviorState.MovementTarget.IsMoving = true;
                behaviorState.MovementTarget.TargetPlayer = null;
            }

            // Enable movement, disable attack
            npc.AutomaticMoveEnabled = true;
            npc.AutomaticAttackEnabled = false;
            npc.PlayCw = null;
        }
        
        /// <summary>
        /// Handle immediate counter-attack when monster is attacked by player
        /// Monster will immediately target and attack the player if not already engaged with another player
        /// </summary>
        public static void HandleMonsterCounterAttack(NpcClass npc, Players attacker)
        {
            try
            {
                if (npc == null || attacker == null || npc.NPCDeath || attacker.NhanVat_HP <= 0 || attacker.PlayerTuVong)
                    return;

                // Check if monster is already engaged with another player
                var currentTarget = GetCurrentTarget(npc);

                // If no current target, or current target is the same as attacker, immediately counter-attack
                if (currentTarget == null || currentTarget.SessionID == attacker.SessionID)
                {
                    // Add attacker to PlayList if not already there
                    if (npc.GetNearbyPlayersCount() == 0 || !npc.IsPlayerInRange(attacker))
                    {
                        npc.AddPlayerTarget(attacker);
                    }

                    // Add to damage tracking (AddPlayerTarget for targeting)
                    npc.AddPlayerTarget(attacker);

                    // Calculate distance to attacker
                    var distanceToAttacker = CalculateDistance(npc.Rxjh_X, npc.Rxjh_Y, attacker.PosX, attacker.PosY);

                    // Determine immediate behavior - always attack when counter-attacking
                    NPCBehaviorType behavior = NPCBehaviorType.Attacking;

                    // Execute immediate counter-attack behavior
                    ExecuteBehavior(npc, behavior, attacker);
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error in HandleMonsterCounterAttack for NPC {npc.NPC_SessionID}: {ex.Message}");
            }
        }

        /// <summary>
        /// Get the current target player for the NPC based on PlayGj (damage tracking)
        /// </summary>
        private static Players GetCurrentTarget(NpcClass npc)
        {
            try
            {
                var targetSessionID = npc.PlayerWid; // This gets the player with highest damage/priority
                if (targetSessionID > 0 && World.allConnectedChars.TryGetValue(targetSessionID, out var targetPlayer))
                {
                    // Ignore GM players (GMMode = 8) and check if player is in range
                    if (targetPlayer.NhanVat_HP > 0 && !targetPlayer.PlayerTuVong &&
                        targetPlayer.GMMode != 8 && npc.IsPlayerInRange(targetPlayer))
                    {
                        return targetPlayer;
                    }
                }
                return null;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error getting current target for NPC {npc.NPC_SessionID}: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Clean up tracking data for removed NPC
        /// </summary>
        public static void CleanupNPC(int npcSessionID)
        {
            try
            {
                _lastMovementTime.TryRemove(npcSessionID, out _);
                _lastPositionUpdate.TryRemove(npcSessionID, out _);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error cleaning up NPC {npcSessionID} tracking data: {ex.Message}");
            }
        }

        /// <summary>
        /// Clean up old entries to prevent memory leak
        /// Should be called periodically
        /// </summary>
        public static void CleanupOldEntries()
        {
            try
            {
                var cutoffTime = DateTime.Now.AddMinutes(-10); // Remove entries older than 10 minutes
                var keysToRemove = new List<int>();

                // Check movement time entries
                foreach (var kvp in _lastMovementTime)
                {
                    if (kvp.Value < cutoffTime)
                    {
                        keysToRemove.Add(kvp.Key);
                    }
                }

                foreach (var key in keysToRemove)
                {
                    _lastMovementTime.TryRemove(key, out _);
                }

                keysToRemove.Clear();

                // Check position update entries
                foreach (var kvp in _lastPositionUpdate)
                {
                    if (kvp.Value < cutoffTime)
                    {
                        keysToRemove.Add(kvp.Key);
                    }
                }

                foreach (var key in keysToRemove)
                {
                    _lastPositionUpdate.TryRemove(key, out _);
                }

                if (keysToRemove.Count > 0)
                {
                    LogHelper.WriteLine(LogLevel.Info, $"Cleaned up {keysToRemove.Count} old throttling entries");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error cleaning up old throttling entries: {ex.Message}");
            }
        }

        /// <summary>
        /// Reset NPC state to fix stuck behavior
        /// </summary>
        public static void ResetNPCState(NpcClass npc)
        {
            try
            {
                // Clear movement throttling
                _lastMovementTime.TryRemove(npc.NPC_SessionID, out _);
                _lastPositionUpdate.TryRemove(npc.NPC_SessionID, out _);

                // Clear behavior state
                RemoveBehaviorState(npc.NPC_SessionID);

                // Clear movement state
                _npcMovementStates.TryRemove(npc.NPC_SessionID, out _);

                // Reset NPC flags
                npc.AutomaticMoveEnabled = true;
                npc.AutomaticAttackEnabled = false;

                // Clear target
                npc.PlayCw = null;

                // Force position update
                npc.SendMovingData(npc.Rxjh_X, npc.Rxjh_Y, 50, 2);

                LogHelper.WriteLine(LogLevel.Info, $"Reset state for NPC {npc.Name} (SessionID: {npc.NPC_SessionID})");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error resetting NPC state for {npc.NPC_SessionID}: {ex.Message}");
            }
        }

        #region Configuration Setters

        /// <summary>
        /// Set attack range for NPCs
        /// </summary>
        public static void SetAttackRange(int range)
        {
            if (range > 0 && range <= 1000)
            {
                ATTACK_RANGE = range;
                LogHelper.WriteLine(LogLevel.Info, $"NPCBehaviorManager: Attack range set to {range}");
            }
            else
            {
                throw new ArgumentException("Attack range must be between 1 and 1000");
            }
        }

        /// <summary>
        /// Set chase range for NPCs
        /// </summary>
        public static void SetChaseRange(int range)
        {
            if (range > 0 && range <= 1000)
            {
                CHASE_RANGE = range;
                LogHelper.WriteLine(LogLevel.Info, $"NPCBehaviorManager: Chase range set to {range}");
            }
            else
            {
                throw new ArgumentException("Chase range must be between 1 and 1000");
            }
        }

        /// <summary>
        /// Set activity area for NPCs
        /// </summary>
        public static void SetActivityArea(int area)
        {
            if (area > 0 && area <= 2000)
            {
                ACTIVITY_AREA = area;
                LogHelper.WriteLine(LogLevel.Info, $"NPCBehaviorManager: Activity area set to {area}");
            }
            else
            {
                throw new ArgumentException("Activity area must be between 1 and 2000");
            }
        }

        /// <summary>
        /// Set return to spawn range for NPCs
        /// </summary>
        public static void SetReturnToSpawnRange(int range)
        {
            if (range > 0 && range <= 2000)
            {
                RETURN_TO_SPAWN_RANGE = range;
                LogHelper.WriteLine(LogLevel.Info, $"NPCBehaviorManager: Return to spawn range set to {range}");
            }
            else
            {
                throw new ArgumentException("Return to spawn range must be between 1 and 2000");
            }
        }

        /// <summary>
        /// Set movement throttle time in milliseconds
        /// </summary>
        public static void SetMovementThrottle(int milliseconds)
        {
            if (milliseconds >= 100 && milliseconds <= 10000)
            {
                MOVEMENT_THROTTLE_MS = milliseconds;
                LogHelper.WriteLine(LogLevel.Info, $"NPCBehaviorManager: Movement throttle set to {milliseconds}ms");
            }
            else
            {
                throw new ArgumentException("Movement throttle must be between 100 and 10000 milliseconds");
            }
        }

        /// <summary>
        /// Set position update throttle time in milliseconds
        /// </summary>
        public static void SetPositionUpdateThrottle(int milliseconds)
        {
            if (milliseconds >= 100 && milliseconds <= 5000)
            {
                POSITION_UPDATE_THROTTLE_MS = milliseconds;
                LogHelper.WriteLine(LogLevel.Info, $"NPCBehaviorManager: Position update throttle set to {milliseconds}ms");
            }
            else
            {
                throw new ArgumentException("Position update throttle must be between 100 and 5000 milliseconds");
            }
        }

        /// <summary>
        /// Reset all settings to default values
        /// </summary>
        public static void ResetToDefaults()
        {
            ATTACK_RANGE = 10;
            CHASE_RANGE = 100;
            ACTIVITY_AREA = 500;
            RETURN_TO_SPAWN_RANGE = 600;
            MOVEMENT_THROTTLE_MS = 1000;
            POSITION_UPDATE_THROTTLE_MS = 500;

            LogHelper.WriteLine(LogLevel.Info, "NPCBehaviorManager: All settings reset to defaults");
        }

        /// <summary>
        /// Get throttling statistics for debugging
        /// </summary>
        public static (int MovementEntries, int PositionEntries) GetThrottlingStats()
        {
            try
            {
                return (_lastMovementTime.Count, _lastPositionUpdate.Count);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error getting throttling stats: {ex.Message}");
                return (0, 0);
            }
        }

        #endregion

        #region Smart Positioning Methods

        /// <summary>
        /// Calculate optimal attack position around player to avoid NPC clustering
        /// </summary>
        private static (float targetX, float targetY) CalculateOptimalAttackPosition(NpcClass npc, Players targetPlayer)
        {
            // Get all NPCs near the target player to avoid clustering
            var nearbyNPCs = GetNearbyNPCs(targetPlayer, ATTACK_RANGE * 3);

            // Calculate preferred angle based on NPC's current position relative to player
            var currentAngle = Math.Atan2(npc.Rxjh_Y - targetPlayer.PosY, npc.Rxjh_X - targetPlayer.PosX);

            // Try to find an optimal position around the player
            var bestAngle = FindBestPositionAngle(targetPlayer, nearbyNPCs, currentAngle, npc.NPC_SessionID);

            // Dynamic attack distance based on number of nearby NPCs
            var baseAttackDistance = ATTACK_RANGE * 0.5f;
            var npcCount = nearbyNPCs.Count;

            // Increase distance when more NPCs are around to spread them out
            var dynamicDistance = baseAttackDistance;
            if (npcCount > 3)
            {
                dynamicDistance = baseAttackDistance + (npcCount - 3) * 5f; // Add 5 pixels per extra NPC
                dynamicDistance = Math.Min(dynamicDistance, ATTACK_RANGE * 1.0f); // Cap at 1.5x attack range
            }

            var targetX = targetPlayer.PosX + (float)(Math.Cos(bestAngle) * dynamicDistance);
            var targetY = targetPlayer.PosY + (float)(Math.Sin(bestAngle) * dynamicDistance);

            return (targetX, targetY);
        }




        /// <summary>
        /// Find the best angle around a target to minimize clustering using random 360-degree positioning
        /// </summary>
        private static double FindBestPositionAngle(Players targetPlayer, List<NpcClass> nearbyNPCs, double preferredAngle, int excludeNpcId, float centerX = 0, float centerY = 0)
        {
            if (centerX == 0 && centerY == 0)
            {
                centerX = targetPlayer.PosX;
                centerY = targetPlayer.PosY;
            }

            var random = new Random(excludeNpcId + DateTime.Now.Millisecond);
            var bestAngle = preferredAngle;
            var minCrowding = float.MaxValue;

            // Test multiple random angles to find the best position
            var testAngles = 16; // Number of random angles to test

            for (int i = 0; i < testAngles; i++)
            {
                // Generate completely random angle (0 to 2π)
                var angle = random.NextDouble() * 2 * Math.PI;

                // Calculate position at this angle
                var testX = centerX + (float)(Math.Cos(angle) * ATTACK_RANGE * 0.8f);
                var testY = centerY + (float)(Math.Sin(angle) * ATTACK_RANGE * 0.8f);

                // Calculate crowding score (how many NPCs are near this position)
                var crowdingScore = 0f;
                foreach (var nearbyNpc in nearbyNPCs)
                {
                    if (nearbyNpc.NPC_SessionID == excludeNpcId) continue;

                    var distance = CalculateDistance(testX, testY, nearbyNpc.Rxjh_X, nearbyNpc.Rxjh_Y);
                    if (distance < ATTACK_RANGE)
                    {
                        // Exponential penalty for closer NPCs to strongly discourage clustering
                        var proximityFactor = (ATTACK_RANGE - distance) / ATTACK_RANGE;
                        crowdingScore += (float)(proximityFactor * proximityFactor * 2); // Squared penalty
                    }
                }

                // Small preference for angles closer to the preferred angle (but much less weight than crowding)
                var angleDifference = Math.Abs(angle - preferredAngle);
                if (angleDifference > Math.PI) angleDifference = 2 * Math.PI - angleDifference;
                var anglePreferenceScore = (float)angleDifference / (float)Math.PI * 0.1f; // Very low weight

                var totalScore = crowdingScore + anglePreferenceScore;

                if (totalScore < minCrowding)
                {
                    minCrowding = totalScore;
                    bestAngle = angle;
                }
            }

            // If all tested positions are crowded, fall back to completely random angle
            if (minCrowding > 1.0f)
            {
                bestAngle = random.NextDouble() * 2 * Math.PI;
            }

            return bestAngle;
        }

        /// <summary>
        /// Get all NPCs near a target player within specified range
        /// </summary>
        private static List<NpcClass> GetNearbyNPCs(Players targetPlayer, float range)
        {
            var nearbyNPCs = new List<NpcClass>();

            try
            {
                //var listNpc = targetPlayer.GetNpcsInRangeZone((int)range);
                var listNpc = targetPlayer.NpcList.Values;
                foreach (var npc in listNpc)
                {
                    if (npc.NPCDeath || npc.FLD_AT <= 0) continue;

                    var distance = CalculateDistance(npc.Rxjh_X, npc.Rxjh_Y, targetPlayer.PosX, targetPlayer.PosY);
                    if (distance <= range)
                    {
                        nearbyNPCs.Add(npc);
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error getting nearby NPCs: {ex.Message}");
            }

            return nearbyNPCs;
        }

        #endregion

        #region Debug and Monitoring

        /// <summary>
        /// Get behavior state information for debugging
        /// </summary>
        public static string GetBehaviorStateInfo(int npcSessionID)
        {
            if (_npcBehaviorStates.TryGetValue(npcSessionID, out var state))
            {
                var elapsed = (DateTime.Now - state.BehaviorStartTime).TotalMilliseconds;
                return $"NPC {npcSessionID}: {state.CurrentBehavior} for {elapsed:F0}ms (min: {state.MinimumDuration}ms), " +
                       $"CanChange: {state.CanChangeBehavior()}, Target: {state.LastTarget?.CharacterName ?? "None"}";
            }
            return $"NPC {npcSessionID}: No behavior state found";
        }

        /// <summary>
        /// Get all behavior states for debugging
        /// </summary>
        public static void LogAllBehaviorStates()
        {
            LogHelper.WriteLine(LogLevel.Info, $"=== NPC Behavior States ({_npcBehaviorStates.Count} NPCs) ===");
            foreach (var kvp in _npcBehaviorStates)
            {
                LogHelper.WriteLine(LogLevel.Info, GetBehaviorStateInfo(kvp.Key));
            }
            LogHelper.WriteLine(LogLevel.Info, "=== End Behavior States ===");
        }

        /// <summary>
        /// Force unlock behavior for debugging
        /// </summary>
        public static void ForceUnlockBehavior(int npcSessionID)
        {
            if (_npcBehaviorStates.TryGetValue(npcSessionID, out var state))
            {
                state.IsLocked = false;
                state.BehaviorStartTime = DateTime.Now.AddMilliseconds(-state.MinimumDuration);
                LogHelper.WriteLine(LogLevel.Info, $"Force unlocked behavior for NPC {npcSessionID}");
            }
        }

        #endregion
    }
    
    /// <summary>
    /// NPC behavior types - Redesigned movement system
    /// </summary>
    public enum NPCBehaviorType
    {
        Idle,           // Standing still, not moving
        Moving,         // Random movement around spawn area
        Attacking,      // Chasing and attacking player
        Returning       // Returning to spawn point
    }
}
