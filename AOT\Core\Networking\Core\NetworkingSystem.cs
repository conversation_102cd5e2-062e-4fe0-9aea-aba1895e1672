using System;
using System.Threading.Tasks;
using Akka.Actor;
using Akka.Actor.Setup;
using Akka.Configuration;
using HeroYulgang.Services;

namespace HeroYulgang.Core.Networking.Core
{
    /// <summary>
    /// Quản lý ActorSystem cho toàn bộ hệ thống mạng
    /// Thay thế cho ActorSystemManager với namespace mới
    /// </summary>
    public class NetworkingSystem
    {
        private static NetworkingSystem? _instance;
        private ActorSystem? _actorSystem;
        private IActorRef? _tcpManagerActor;

        public static NetworkingSystem Instance => _instance ??= new NetworkingSystem();

        public ActorSystem ActorSystem => _actorSystem ?? throw new InvalidOperationException("ActorSystem chưa được khởi tạo");

        public IActorRef TcpManagerActor => _tcpManagerActor ?? throw new InvalidOperationException("TcpManagerActor chưa được khởi tạo");

        private NetworkingSystem()
        {
            // Private constructor for singleton pattern
        }

        /// <summary>
        /// Khởi tạo ActorSystem và các actor chính
        /// </summary>
        public async Task InitializeAsync()
        {
            try
            {
                Logger.Instance.Info("Bắt đầu khởi tạo NetworkingSystem...");

                // Thử tạo ActorSystem với timeout để tránh hang
                var cts = new System.Threading.CancellationTokenSource(TimeSpan.FromSeconds(30));

                await Task.Run(() =>
                {
                    try
                    {
                        Logger.Instance.Info("Đang tạo ActorSystem với tên 'HeroYulgangNetworking'...");

                        try
                        {
                            // Tạo optimized configuration cho high-performance
                            Logger.Instance.Info("Tạo ActorSystem với optimized configuration...");
                            var config = ConfigurationFactory.ParseString(@"
                                akka {
                                    actor {
                                        default-dispatcher {
                                            type = Dispatcher
                                            executor = fork-join-executor
                                            fork-join-executor {
                                                parallelism-min = 4
                                                parallelism-factor = 2.0
                                                parallelism-max = 16
                                            }
                                            throughput = 100
                                        }

                                        default-mailbox {
                                            mailbox-type = ""Akka.Dispatch.UnboundedMailbox""
                                            mailbox-capacity = 10000
                                        }
                                    }

                                    io.tcp {
                                        nr-of-selectors = 2
                                        max-channels = 2048
                                        selector-association-retries = 10
                                        batch-accept-limit = 10
                                        direct-buffer-size = 128KiB
                                        direct-buffer-pool-limit = 1000
                                    }
                                }
                            ");
                            _actorSystem = ActorSystem.Create("HeroYulgangNetworking", config);
                            Logger.Instance.Info("Tạo ActorSystem thành công với optimized configuration");
                        }
                        catch (Exception ex1)
                        {
                            Logger.Instance.Error($"Lỗi khi tạo ActorSystem với optimized configuration: {ex1.Message}");

                            try
                            {
                                // Phương pháp 2: Thử tạo ActorSystem đơn giản nhất
                                Logger.Instance.Info("Thử tạo ActorSystem với default settings...");
                                _actorSystem = ActorSystem.Create("HeroYulgangNetworking");
                                Logger.Instance.Info("Tạo ActorSystem thành công với default settings");
                            }
                            catch (Exception ex2)
                            {
                                Logger.Instance.Error($"Lỗi khi tạo ActorSystem với default settings: {ex2.Message}");

                                try
                                {
                                    // Phương pháp 3: Sử dụng ActorSystemSetup để bypass configuration
                                    Logger.Instance.Info("Thử tạo ActorSystem với ActorSystemSetup...");
                                    var setup = ActorSystemSetup.Create();
                                    _actorSystem = ActorSystem.Create("HeroYulgangNetworking", setup);
                                    Logger.Instance.Info("Tạo ActorSystem thành công với ActorSystemSetup");
                                }
                                catch (Exception ex3)
                                {
                                    Logger.Instance.Error($"Lỗi khi tạo ActorSystem với ActorSystemSetup: {ex3.Message}");
                                    throw new InvalidOperationException($"Không thể tạo ActorSystem. Lỗi 1: {ex1.Message}, Lỗi 2: {ex2.Message}, Lỗi 3: {ex3.Message}");
                                }
                            }
                        }

                        Logger.Instance.Info("ActorSystem đã được tạo thành công");

                        if (_actorSystem == null)
                        {
                            throw new InvalidOperationException("ActorSystem.Create returned null");
                        }

                        Logger.Instance.Info("Đang tạo TcpManagerActor...");

                        // Tạo TcpManagerActor
                        _tcpManagerActor = _actorSystem.ActorOf(Props.Create<TcpManagerActor>(), "tcpManager");

                        if (_tcpManagerActor == null)
                        {
                            throw new InvalidOperationException("Failed to create TcpManagerActor");
                        }

                        Logger.Instance.Info("TcpManagerActor đã được tạo thành công");
                    }
                    catch (Exception ex)
                    {
                        Logger.Instance.Error($"Lỗi trong Task.Run: {ex.Message}");
                        throw;
                    }
                }, cts.Token);

                Logger.Instance.Info("NetworkingSystem đã được khởi tạo thành công");
            }
            catch (OperationCanceledException)
            {
                Logger.Instance.Error("Timeout khi khởi tạo NetworkingSystem (30 giây)");
                throw new TimeoutException("NetworkingSystem initialization timed out after 30 seconds");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi khởi tạo NetworkingSystem: {ex.Message}");
                Logger.Instance.Error($"Stack trace: {ex.StackTrace}");
                if (ex.InnerException != null)
                {
                    Logger.Instance.Error($"Inner exception: {ex.InnerException.Message}");
                    Logger.Instance.Error($"Inner stack trace: {ex.InnerException.StackTrace}");
                }
                throw;
            }
        }

        /// <summary>
        /// Khởi tạo NetworkingSystem (sync version for backward compatibility)
        /// </summary>
        public void Initialize()
        {
            InitializeAsync().GetAwaiter().GetResult();
        }

        /// <summary>
        /// Dừng NetworkingSystem
        /// </summary>
        public void Shutdown()
        {
            try
            {
                if (_actorSystem != null)
                {
                    _actorSystem.Terminate().Wait();
                    _actorSystem = null;
                    _tcpManagerActor = null;

                    Logger.Instance.Info("NetworkingSystem đã được dừng thành công");
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi dừng NetworkingSystem: {ex.Message}");
            }
        }
    }
}
