using System;
using HeroYulgang.Core.Networking.Network;
using HeroYulgang.Helpers;
using HeroYulgang.Services;

namespace RxjhServer
{
    public partial class PlayersBes
    {   
        private ActorNetState _actorNetState;
        private bool _useActorNetState = false;
        /// <summary>
        /// Lấy hoặc đặt đối tượng kết nối của người chơi
        /// Có thể là NetState hoặc ActorNetState tùy thuộc vào cấu hình
        /// </summary>
        public ActorNetState Client
        {
            get
            {
                    return _actorNetState;
            }
            set
            {
                if (value is ActorNetState actorNetState)
                {
                    _actorNetState = actorNetState;
                    _useActorNetState = true;
                    LogHelper.WriteLine(LogLevel.Debug, $"Đã chuyển đổi Client của PlayersBes sang ActorNetState");
                }
                else
                {
                    LogHelper.WriteLine(LogLevel.Error, $"Kiểu dữ liệu không hợp lệ cho property Client: {value?.GetType().Name ?? "null"}");
                    throw new ArgumentException("Kiểu dữ liệu không hợp lệ cho property Client", nameof(value));
                }
            }
        }

    }
}
