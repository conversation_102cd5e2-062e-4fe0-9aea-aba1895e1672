

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FreeSql;
using HeroYulgang.Core;
using HeroYulgang.Database.FreeSql.Entities.Game;
using HeroYulgang.Database.FreeSql.Models;
using HeroYulgang.RxjhServer.Attendance;
using HeroYulgang.Services;
using RxjhServer;
using RxjhServer.HelperTools;
namespace HeroYulgang.Database.FreeSql;

public static class GameDb
{
    private static IFreeSql? _freeSql;
    private static readonly Dictionary<int, GuildMember> _guildMemberCache = new();
    private static bool _isInitialized = false;
    private static readonly object _lock = new object();

    public static IFreeSql? FreeSql => _freeSql;

    public static bool InitializeAsync()
    {
        try
        {
            if (_isInitialized) return true;

            lock (_lock)
            {
                if (_isInitialized) return true;

                var connectionString = ConfigManager.Instance.PogresSettings.GameDb;
                // Console.WriteLine("GameDb connection string: " + connectionString);
                // Create FreeSql instance
                _freeSql = new FreeSqlBuilder()
                    .UseConnectionString(DataType.PostgreSQL, connectionString)
                    //.UseAutoSyncStructure(true)
                    .UseAdoConnectionPool(true)
                    // .UseNoneCommandParameter(true)
                    .Build();
                Logger.Instance.Info("✓ GameDb initialized successfully");
                _isInitialized = true;
            }
            return true;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Failed to initialize GameDb: {ex.Message}");
            return false;
        }
    }

    public static List<tbl_group_quest> LoadQuestDefinitions()
    {
        try
        {
            if (_freeSql == null) return null;

            var quests = _freeSql.Select<tbl_group_quest>().Where(q => q.isactive ?? false).ToList();
            return quests;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Failed to load quest definitions: {ex.Message}");
            return null;
        }
    }

    public static async Task LoadGuildMemberAsync()
    {
        try
        {
            if (_freeSql == null) return;

            var guildMembers = await _freeSql.Select<tbl_xwwl_guildmember>().ToListAsync();
            lock (_lock)
            {
                _guildMemberCache.Clear();
                foreach (var member in guildMembers)
                {
                    GuildMember item = new()
                    {
                        ID = member.id,
                        PlayerName = member.fld_name!,
                        GuildName = member.g_name!,
                        Guild_Score = long.Parse(member.fld_guildpoint!.Value.ToString()),
                        Role = member.leve!.Value
                    };
                    _guildMemberCache[member.id] = item;
                    World.GuildList.TryAdd(item.PlayerName, item);
                }
            }
            Logger.Instance.Info($"✓ Loaded {_guildMemberCache.Count} guild members");
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Failed to load guild members: {ex.Message}");
        }
    }
    public async static Task LoadAllRanking()
    {
        try
        {
            World.TheLucChien_XepHang_SoLieu.Clear();
            var rankings = await _freeSql.Select<tbl_vinhduhethong>().Take(20).Where(a => a.fld_type == 1).OrderByDescending(a => a.fld_diemso).ToListAsync();
            for (int i = 0; i < rankings.Count; i++)
            {
                World.TheLucChien_XepHang_SoLieu.Add(i, new IngameRanking
                {
                    RankingType = 1,
                    RankingIndex = i,
                    PlayerName = rankings[i].fld_tennhanvat,
                    GuildName = rankings[i].fld_bangphai,
                    PlayerJob = rankings[i].fld_nghenghiep ?? 0,
                    Faction = rankings[i].fld_theluc ?? 0,
                    PlayerLevel = rankings[i].fld_dangcap ?? 0,
                    Point = rankings[i].fld_diemso ?? 0
                });
            }
            World.VoLamHuyetChien_XepHang_SoLieu.Clear();
            rankings = await _freeSql.Select<tbl_vinhduhethong>().Take(20).Where(a => a.fld_type == 2).OrderByDescending(a => a.fld_diemso).ToListAsync();
            for (int i = 0; i < rankings.Count; i++)
            {
                World.VoLamHuyetChien_XepHang_SoLieu.Add(i, new IngameRanking
                {
                    RankingType = 2,
                    RankingIndex = i,
                    PlayerName = rankings[i].fld_tennhanvat,
                    GuildName = rankings[i].fld_bangphai,
                    PlayerJob = rankings[i].fld_nghenghiep ?? 0,
                    Faction = rankings[i].fld_theluc ?? 0,
                    PlayerLevel = rankings[i].fld_dangcap ?? 0,
                    Point = rankings[i].fld_diemso ?? 0
                });
            }
            World.BangPhai_XepHang_SoLieu.Clear();
            rankings = await _freeSql.Select<tbl_vinhduhethong>().Take(20).Where(a => a.fld_type == 3).OrderByDescending(a => a.fld_diemso).ToListAsync();
            for (int i = 0; i < rankings.Count; i++)
            {
                World.BangPhai_XepHang_SoLieu.Add(i, new IngameRanking
                {
                    RankingType = 3,
                    RankingIndex = i,
                    PlayerName = rankings[i].fld_tennhanvat,
                    GuildName = rankings[i].fld_bangphai,
                    PlayerJob = rankings[i].fld_nghenghiep ?? 0,
                    Faction = rankings[i].fld_theluc ?? 0,
                    PlayerLevel = rankings[i].fld_dangcap ?? 0,
                    Point = rankings[i].fld_diemso ?? 0
                });
            }
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Lỗi khi tải ranking " + ex.Message);
        }
    }

    /// <summary>
    /// Get next batch of item serial IDs from database
    /// Equivalent to xwwl_getitemserial2 stored procedure
    /// Thread-safe implementation for native AOT compatibility
    /// </summary>
    /// <param name="batchSize">Number of IDs to allocate (default: 1000)</param>
    /// <returns>Starting ID of the allocated batch, or -1 if failed</returns>
    private static long GetNextItemSerialBatch(int batchSize = 1000)
    {
        try
        {
            const int serverId = 1; // Default server ID
            const int maxIntValue = 2100000000; // Max value before reset
            const int resetValue = 1000; // Reset value

            // Get current itemcount for server (synchronous to avoid lock issues)
            var serverInfo = _freeSql
                .Select<xwwl_gameserverinfo>()
                .Where(s => s.serverid == serverId)
                .First();

            long currentCount;

            if (serverInfo == null)
            {
                // Create new record with initial value
                _freeSql
                    .Insert(new xwwl_gameserverinfo
                    {
                        serverid = serverId,
                        itemcount = resetValue
                    })
                    .ExecuteAffrows();
                currentCount = resetValue;
            }
            else
            {
                currentCount = serverInfo.itemcount ?? resetValue;
            }

            long newCount;
            long startingId;

            // Check if we need to reset (approaching max int value)
            if (currentCount >= maxIntValue)
            {
                // Reset to initial value
                newCount = resetValue + batchSize;
                startingId = resetValue;

                _freeSql
                    .Update<xwwl_gameserverinfo>()
                    .Set(s => s.itemcount, (int)newCount)
                    .Where(s => s.serverid == serverId)
                    .ExecuteAffrows();
            }
            else
            {
                // Increment by batch size
                newCount = currentCount + batchSize;
                startingId = currentCount + 1;

                _freeSql
                    .Update<xwwl_gameserverinfo>()
                    .Set(s => s.itemcount, (int)newCount)
                    .Where(s => s.serverid == serverId)
                    .ExecuteAffrows();
            }

            Logger.Instance.Debug($"✓ Item serial batch allocated: {startingId} - {startingId + batchSize - 1}");
            return startingId;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Failed to get item serial batch: {ex.Message}");
            return -1;
        }
    }

    /// <summary>
    /// Create unique item serial numbers using FreeSql ORM
    /// Thread-safe implementation with batch allocation from database
    /// Native AOT compatible version
    /// </summary>
    /// <returns>Unique item serial number</returns>
    private static readonly object _itemSeriesLock = new();
    private static long _currentItemId = 0;
    private static long _maxItemId = 0;

    public static long CreateItemSeries()
    {
        lock (_itemSeriesLock)
        {
            try
            {
                // Check if we need to get a new batch of IDs
                if (_currentItemId >= _maxItemId)
                {
                    // Use FreeSql ORM instead of stored procedure for native AOT compatibility
                    // Equivalent to xwwl_getitemserial2 procedure logic
                    var startingId = GetNextItemSerialBatch(1000);

                    if (startingId <= 0)
                    {
                        Logger.Instance.Error("Failed to get item serial from database");
                        return 0;
                    }

                    _currentItemId = startingId;
                    _maxItemId = startingId + 1000;

                    Logger.Instance.Debug($"✓ New item serial batch allocated: {_currentItemId} - {_maxItemId - 1}");
                }

                return _currentItemId++;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"✗ CreateItemSeries Error: {ex.Message}");

                // Fallback: try to generate a simple timestamp-based ID
                try
                {
                    var fallbackId = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
                    Logger.Instance.Warning($"Using fallback item serial: {fallbackId}");
                    return fallbackId;
                }
                catch
                {
                    Logger.Instance.Error("Even fallback item serial generation failed");
                    return 0;
                }
            }
        }
    }

    public static async void GuildWar_MoneyBet_Delete(string userId, string userName, int guildId, int result)
    {
        try
        {
            await _freeSql
            .Delete<bangchien_tiendatcuoc>()
            .Where(a => a.userid == userId && a.username == userName && a.bangphaiid == guildId)
            .ExecuteAffrowsAsync();

            switch (result)
            {
                case -1: // Thua
                    await _freeSql
                    .Update<tbl_xwwl_guild>()
                    .Set(a => a.thua == a.thua + 1)
                    .Where(a => a.id == guildId)
                    .ExecuteAffrowsAsync();
                    break;
                case 0: // Hòa
                    await _freeSql
                    .Update<tbl_xwwl_guild>()
                    .Set(a => a.hoa == a.hoa + 1)
                    .Where(a => a.id == guildId)
                    .ExecuteAffrowsAsync();
                    break;
                case 1: // Thắng
                    await _freeSql
                    .Update<tbl_xwwl_guild>()
                    .Set(a => a.thang == a.thang + 1)
                    .Where(a => a.id == guildId)
                    .ExecuteAffrowsAsync();
                    break;
            }
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Lỗi khi xóa bản ghi tiền đặt cược bang chiến " + ex.Message);
        }
    }

    public static async void GuildWar_MoneyBet(string userId, string userName, int guildId, int amount)
    {
        try
        {
            await _freeSql.Insert(new bangchien_tiendatcuoc
            {
                userid = userId,
                username = userName,
                bangphaiid = guildId,
                nguyenbaosoluong = amount
            }).ExecuteAffrowsAsync();
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Lỗi khi đặt cược bang chiến " + ex.Message);
        }
    }

    public static async void UpdateGuildCostume(int guildId, int word, int color)
    {
        try
        {
            await _freeSql
            .Update<tbl_xwwl_guild>()
            .Set(a => a.monphucword == word)
            .Set(a => a.monphucmausac == color)
            .Where(a => a.id == guildId)
            .ExecuteAffrowsAsync();
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Lỗi khi cập nhật trang phục bang phái " + ex.Message);
        }
    }


    public static async Task SetGuildEmblem(int guildId, byte[] emblem)
    {
        try
        {
            await _freeSql
            .Update<tbl_xwwl_guild>()
            .Set(a => a.monhuy == emblem)
            .Where(a => a.id == guildId)
            .ExecuteAffrowsAsync();
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Lỗi khi cập nhật huy hiệu bang phái " + ex.Message);
        }
    }

    public static async Task<byte[]> GetGuildEmblem(int guildId)
    {
        try
        {
            var guild = await _freeSql
            .Select<tbl_xwwl_guild>()
            .Where(a => a.id == guildId)
            .FirstAsync();
            return guild?.monhuy ?? new byte[100];
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Lỗi khi tải huy hiệu bang phái " + ex.Message);
            return new byte[100];
        }
    }

    public static async Task ChangeGuildMaster(string newMasterName, string oldMasterName, string guildName)
    {
        try
        {
            await _freeSql
            .Update<tbl_xwwl_guildmember>()
            .Set(a => a.leve == 6)
            .Where(a => a.fld_name == oldMasterName && a.g_name == guildName)
            .ExecuteAffrowsAsync();

            await _freeSql
            .Update<tbl_xwwl_guildmember>()
            .Set(a => a.leve == 5)
            .Where(a => a.fld_name == newMasterName && a.g_name == guildName)
            .ExecuteAffrowsAsync();

            await _freeSql
            .Update<tbl_xwwl_guild>()
            .Set(a => a.g_master == newMasterName)
            .Where(a => a.g_name == guildName)
            .ExecuteAffrowsAsync();
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Lỗi khi chuyển bang chủ " + ex.Message);
        }
    }

    /// <summary>
    /// Load Guild Ranking
    /// </summary>
    public static async Task LoadGuildRanking()
    {
        try
        {
            World.BangPhaiXepHangSoLieu.Clear();
            var rankings = await _freeSql.Select<vinhdubangphaixephang>().ToListAsync();
            for (int i = 0; i < rankings.Count; i++)
            {
                World.BangPhaiXepHangSoLieu.Add(new X_Mon_Phai_Xep_Hang
                {
                    BangPhaiBangPhaiTen = rankings[i].fld_bp,
                    BangPhaiTenNhanVat = rankings[i].fld_name,
                    BangPhaiChinhTa = (int)rankings[i].fld_zx,
                    BangPhaiNgheNghiep = (int)rankings[i].fld_job,
                    BangPhaiChuyenChuc = (int)rankings[i].fld_job_level,
                    BangPhaiNhanVat_DangCap = (int)rankings[i].fld_level,
                    BangPhaiVinhDu_DiemSo = (int)rankings[i].fld_ry,
                    BangPhaiPhanKhuID = rankings[i].fld_fq
                });
            }
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Lỗi khi tải guildranking " + ex.Message);
        }
    }
    /// <summary>
    /// Update Guild Position
    /// </summary>
    /// <param name="guildName"></param>
    /// <param name="playerName"></param>
    /// <param name="position"></param>
    /// <returns></returns>

    public static async Task UpdateGuildPosition(string guildName, string playerName, int position)
    {
        try
        {
            await _freeSql
            .Update<tbl_xwwl_guildmember>()
            .Set(a => a.leve == position)
            .Where(a => a.g_name == guildName && a.fld_name == playerName)
            .ExecuteAffrowsAsync();
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Lỗi khi cập nhật guildranking " + ex.Message);
        }
    }

    public static async Task UpdateGuildHonor(int guildId, int honor)
    {
        try
        {
            await _freeSql
            .Update<tbl_xwwl_guild>()
            .Set(a => a.bangphaivohuan == honor)
            .Where(a => a.id == guildId)
            .ExecuteAffrowsAsync();
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Lỗi khi cập nhật guildranking " + ex.Message);
        }
    }

    public static async Task UpdateGuildHonor(string guildName, int honor)
    {
        try
        {
            await _freeSql
            .Update<tbl_xwwl_guild>()
            .Set(a => a.bangphaivohuan == honor)
            .Where(a => a.g_name == guildName)
            .ExecuteAffrowsAsync();
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Lỗi khi cập nhật guildranking " + ex.Message);
        }
    }


    /// <summary>
    /// Update Guild Point
    /// </summary>
    /// <param name="guildName"></param>
    /// <param name="point"></param>

    public static async void UpdateGuildPoint(string guildName, int point)
    {
        try
        {
            await _freeSql
            .Update<tbl_xwwl_guildmember>()
            .Set(a => a.fld_guildpoint == point)
            .Where(a => a.g_name == guildName)
            .ExecuteAffrowsAsync();
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Lỗi khi cập nhật guildranking " + ex.Message);
        }
    }


    #region Guild

    public static async Task<tbl_xwwl_guild> FindGuildByMaster(string name)
    {
        try
        {
            return await _freeSql
            .Select<tbl_xwwl_guild>()
            .Where(a => a.g_master == name && a.active == true)
            .FirstAsync();
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Lỗi khi tìm bang phái " + ex.Message);
            return null;
        }
    }

    public static async Task<bool> UpdateGuildLevel(string guildName, int level)
    {
        try
        {
            var result = await _freeSql
            .Update<tbl_xwwl_guild>()
            .Set(a => a.leve == level)
            .Where(a => a.g_name == guildName)
            .ExecuteAffrowsAsync();
            return result > 0;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Lỗi khi cập nhật guildranking " + ex.Message);
            return false;
        }
    }


    public static async Task<bool> CheckGuildHasNewPoint()
    {
        try
        {
            var result = await _freeSql
            .Select<tbl_xwwl_guildmember>()
            .Where(a => a.fld_newguildpoint > 0)
            .CountAsync();
            return result > 0;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Lỗi khi kiểm tra bang phái " + ex.Message);
            return false;
        }
    }

    public static async Task<bool> CheckplayerHasGuild(string playerName)
    {
        try
        {
            var existingMember = await _freeSql
                .Select<tbl_xwwl_guildmember>()
                .Where(a => a.fld_name == playerName)
                .FirstAsync();

            return existingMember != null;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Lỗi khi kiểm tra bang phái " + ex.Message);
            return false;
        }
    }

    public static async Task<bool> UpdateGuildMemberLevel(string guildName, string playerName, int level)
    {
        try
        {
            var result = await _freeSql
            .Update<tbl_xwwl_guildmember>()
            .Set(a => a.fld_level == level)
            .Where(a => a.g_name == guildName && a.fld_name == playerName)
            .ExecuteAffrowsAsync();
            return result > 0;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Lỗi khi cập nhật guildranking " + ex.Message);
            return false;
        }
    }

    public static async Task<bool> CheckGuildNameExists(string guildName)
    {
        try
        {
            var existingGuild = await _freeSql
                .Select<tbl_xwwl_guild>()
                .Where(a => a.g_name == guildName)
                .FirstAsync();

            return existingGuild != null;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Lỗi khi kiểm tra tên bang phái " + ex.Message);
            return false;
        }
    }

    public static async Task<tbl_xwwl_guild> FindGuild(string guildName)
    {
        try
        {
            return await _freeSql
                .Select<tbl_xwwl_guild>()
                .Where(a => a.g_name == guildName)
                .FirstAsync();
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Lỗi khi tìm bang phái " + ex.Message);
            return null;
        }
    }

    public static async Task<List<tbl_xwwl_guildmember>> FindGuildMembers(string guildName)
    {
        try
        {
            return await _freeSql
                .Select<tbl_xwwl_guildmember>()
                .Where(a => a.g_name == guildName && a.active == true)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Lỗi khi đếm số thành viên trong bang phái " + ex.Message);
            return null;
        }
    }

    public static async Task<int> TotalMemberInGuild(string guildName)
    {
        try
        {
            return (int)await _freeSql
                .Select<tbl_xwwl_guildmember>()
                .Where(a => a.g_name == guildName && a.active == true)
                .CountAsync();
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Lỗi khi đếm số thành viên trong bang phái " + ex.Message);
            return 0;
        }
    }

    public static async Task<bool> PlayerJoinGuild(string playerName, string guildName, int playerLevel)
    {
        try
        {

            // find player
            var existingMember = await _freeSql
                .Select<tbl_xwwl_guildmember>()
                .Where(a => a.fld_name == playerName && a.g_name == guildName)
                .FirstAsync();

            // if player is already in guild, update their status to active and return true
            if (existingMember != null)
            {
                await _freeSql
                .Update<tbl_xwwl_guildmember>()
                .Set(a => a.active == true)
                .Set(a => a.updatedat == DateTime.Now)
                .Where(a => a.fld_name == playerName && a.g_name == guildName)
                .ExecuteAffrowsAsync();
                return true;
            }

            // if player is not in guild, create a new guild member and return true
            var guildMember = new tbl_xwwl_guildmember
            {
                fld_name = playerName,
                g_name = guildName,
                leve = 1,
                fld_level = playerLevel,
                fld_guildpoint = 0,
                fld_newguildpoint = 0
            };

            var result = await _freeSql
                .Insert(guildMember)
                .ExecuteAffrowsAsync();

            return result > 0;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Lỗi khi thêm bang viên " + ex.Message);
            return false;
        }

    }

    public static async Task<bool> DisbandGuild(string guildName)
    {
        try
        {
            var result = await _freeSql
           .Update<tbl_xwwl_guild>()
           .Set(a => a.active == false)
           .Set(a => a.updatedat == DateTime.Now)
           .Where(a => a.g_name == guildName)
           .ExecuteAffrowsAsync();

            await _freeSql
             .Update<tbl_xwwl_guildmember>()
             .Set(a => a.active == false)
             .Set(a => a.updatedat == DateTime.Now)
             .Where(a => a.g_name == guildName)
             .ExecuteAffrowsAsync();
            return result > 0;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Lỗi khi giải tán bang phái " + ex.Message);
            return false;
        }
    }



    public static async Task<bool> ExpelMember(string guildName, string playerName)
    {
        try
        {
            var result = await _freeSql
           .Update<tbl_xwwl_guildmember>()
           .Set(a => a.active == false)
           .Set(a => a.updatedat == DateTime.Now)
           .Where(a => a.g_name == guildName && a.fld_name == playerName)
           .ExecuteAffrowsAsync();
            return result > 0;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Lỗi khi đuổi bang viên " + ex.Message);
            return false;
        }
    }

    public static async Task<bool> UpdateGuildNotice(string guildName, string notice)
    {
        try
        {
            var result = await _freeSql
           .Update<tbl_xwwl_guild>()
           .Set(a => a.g_notice == notice)
           .Where(a => a.g_name == guildName)
           .ExecuteAffrowsAsync();
            return result > 0;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Lỗi khi cập nhật thông báo bang phái " + ex.Message);
            return false;
        }
    }


    /// <summary>
    /// Create a new guild
    /// Tạo bang phái mới
    /// </summary>
    /// <param name="masterName">Guild master name - Tên bang chủ</param>
    /// <param name="guildName">Guild name - Tên bang phái</param>
    /// <param name="position">Guild master position/level - Cấp độ bang chủ</param>
    /// <returns>True if guild created successfully, False if failed or already exists</returns>
    public static async Task<bool> CreateGuild(string masterName, string guildName, int position)
    {
        try
        {
            // Validate input parameters
            if (string.IsNullOrEmpty(masterName) || string.IsNullOrEmpty(guildName))
            {
                Logger.Instance.Error("Guild master name or guild name cannot be null or empty");
                return false;
            }

            // Check if guild name already exists
            var existingGuild = await _freeSql
                .Select<tbl_xwwl_guild>()
                .Where(a => a.g_name == guildName)
                .FirstAsync();

            if (existingGuild != null)
            {
                Logger.Instance.Error($"Guild name '{guildName}' already exists");
                return false;
            }

            // Check if master is already a guild master or member
            var existingMaster = await _freeSql
                .Select<tbl_xwwl_guild>()
                .Where(a => a.g_master == masterName)
                .FirstAsync();

            if (existingMaster != null)
            {
                Logger.Instance.Error($"Player '{masterName}' is already a guild master of '{existingMaster.g_name}'");
                return false;
            }

            // Check if master is already a member of another guild
            var existingMembership = await _freeSql
                .Select<tbl_xwwl_guildmember>()
                .Where(a => a.fld_name == masterName)
                .FirstAsync();

            if (existingMembership != null)
            {
                Logger.Instance.Error($"Player '{masterName}' is already a member of guild '{existingMembership.g_name}'");
                return false;
            }

            // Create new guild
            var guild = new tbl_xwwl_guild
            {
                g_name = guildName,                    // Guild name (unique)
                g_master = masterName,                 // Guild master name
                leve = position,                       // Guild master position/level
                thanhdanh = 0,                         // Guild reputation
                monhuy = new byte[100],                // Guild emblem data
                monphucword = 0,                       // Guild uniform word
                monphucmausac = 0,                     // Guild uniform color
                bangphaivohuan = 0,                    // Guild honor points
                thang = 0,                             // Guild wins
                thua = 0,                              // Guild losses
                hoa = 0,                               // Guild draws
                monphaitaisan = string.Empty,          // Guild assets
                thongbao_congthanh = 0,                // Guild siege announcement
                lienminh_minhchu = string.Empty,       // Alliance leader
                g_notice = string.Empty                // Guild notice/announcement
            };

            // Insert guild into database
            var result = await _freeSql
                .Insert(guild)
                .ExecuteAffrowsAsync();

            if (result > 0)
            {
                // Add guild master as the first member
                var guildMaster = new tbl_xwwl_guildmember
                {
                    fld_name = masterName,             // Member name
                    g_name = guildName,                // Guild name
                    leve = 6,                          // Guild master level (highest)
                    fld_level = position,              // Character level
                    fld_guildpoint = 0,                // Guild contribution points
                    fld_newguildpoint = 0              // New guild points
                };

                var memberResult = await _freeSql
                    .Insert(guildMaster)
                    .ExecuteAffrowsAsync();

                if (memberResult > 0)
                {
                    Logger.Instance.Info($"✓ Guild '{guildName}' created successfully with master '{masterName}'");
                    return true;
                }
                else
                {
                    // Rollback: Delete the guild if member creation failed
                    await _freeSql
                        .Delete<tbl_xwwl_guild>()
                        .Where(a => a.g_name == guildName)
                        .ExecuteAffrowsAsync();

                    Logger.Instance.Error($"✗ Failed to add guild master as member, guild creation rolled back");
                    return false;
                }
            }
            else
            {
                Logger.Instance.Error($"✗ Failed to insert guild '{guildName}' into database");
                return false;
            }
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Error creating guild '{guildName}': {ex.Message}");
            return false;
        }
    }



    public static async Task<List<tbl_xwwl_guild>> LoadGuildAlliance(string guildName)
    {
        try
        {
            return await _freeSql
            .Select<tbl_xwwl_guild>()
            .Where(a => a.lienminh_minhchu != null && a.lienminh_minhchu == guildName)
            .ToListAsync();
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Error Loading guild alliance " + ex.Message);
            return null;
        }
    }

    /// <summary>
    /// Load Guild Alliance Load thông tin liên minh của bang phái
    /// </summary>
    /// <returns></returns>

    public static async Task<List<tbl_xwwl_guild>> LoadGuildAlliances()
    {
        try
        {
            return await _freeSql
            .Select<tbl_xwwl_guild>()
            .Where(a => a.lienminh_minhchu != null && a.lienminh_minhchu == a.g_name)
            .ToListAsync();
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Error Loading guild alliance " + ex.Message);
            return null;
        }
    }
    /// <summary>
    /// Load Guild Alliance Name Lấy tên chủ liên minh
    /// </summary>
    /// <param name="guildName"></param>
    /// <returns></returns>

    public static async Task<string> LoadGuildAllianceName(string guildName)
    {
        try
        {
            var guild = await _freeSql
            .Select<tbl_xwwl_guild>()
            .Where(a => a.g_name == guildName)
            .FirstAsync();
            return guild?.lienminh_minhchu ?? string.Empty;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Error Loading guild alliance name " + ex.Message);
            return string.Empty;
        }
    }


    #endregion

    #region GuildQuest

    public static async Task<List<tbl_group_quest>> LoadGroupQuests()
    {
        try
        {
            return await _freeSql
            .Select<tbl_group_quest>()
            .Where(a => a.isactive == true)
            .ToListAsync();
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Error Loading group quests " + ex.Message);
            return null;
        }
    }

    public static async Task<tbl_group_quest_contribution> LoadCompletedQuestContributed(string charactername, int questId, int guildId)
    {
        try
        {
            return await _freeSql
            .Select<tbl_group_quest_contribution>()
            .Where(a => a.id == questId && a.playername == charactername && a.guildid == guildId && a.hasreceivedreward == false)
            .FirstAsync();
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Error Loading group quest " + ex.Message);
            return null;
        }
    }

    public static async Task<int> TotalProcessingQuestToday(int questId, int guildId)
    {
        try
        {
            return (int)await _freeSql
            .Select<tbl_guild_quest_progress>()
            .Where(a => (a.status == 2 || a.status == 1) && a.id == questId && a.guildid == guildId && a.completedtime >= DateTime.Now.Date && a.completedtime < DateTime.Now.Date.AddDays(1))
            .CountAsync();
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Error Loading group quest " + ex.Message);
            return 0;
        }
    }
    public static async Task<int> TotalCompletedQuestToday(int questId, int guildId)
    {
        try
        {
            return (int)await _freeSql
            .Select<tbl_guild_quest_progress>()
            .Where(a => a.status == 3 && a.id == questId && a.guildid == guildId && a.completedtime >= DateTime.Now.Date && a.completedtime < DateTime.Now.Date.AddDays(1))
            .CountAsync();
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Error Loading group quest " + ex.Message);
            return 0;
        }
    }


    public static async Task<bool> UpdateReceivedQuestContributedReward(string charactername, int questId, int guildId)
    {
        try
        {
            var result = await _freeSql
            .Update<tbl_group_quest_contribution>()
            .Set(a => a.hasreceivedreward == true)
            .Where(a => a.id == questId && a.playername == charactername && a.guildid == guildId)
            .ExecuteAffrowsAsync();
            return result > 0;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Error Updating group quest " + ex.Message);
            return false;
        }
    }


    #endregion

    #region Character Data Management

    /// <summary>
    /// Save character data using PostgreSQL stored procedure
    /// Lưu dữ liệu nhân vật sử dụng PostgreSQL stored procedure
    /// </summary>
    /// <param name="characterData">Character data object containing all player information</param>
    /// <returns>True if successful, false if failed</returns>
    public static async Task<bool> SaveCharacterDataAsync(CharacterDataModel characterData)
    {
        try
        {
            if (_freeSql == null)
            {
                Logger.Instance.Error("FreeSql instance is not initialized");
                return false;
            }

            if (characterData == null)
            {
                Logger.Instance.Error("Character data is null");
                return false;
            }

            if (string.IsNullOrEmpty(characterData.AccountID) || string.IsNullOrEmpty(characterData.CharacterName))
            {
                Logger.Instance.Error("Account ID or Character Name is null or empty");
                return false;
            }

            // Use FreeSql ORM Update method instead of stored procedure for native AOT compatibility
            // Sử dụng phương thức Update của FreeSql ORM thay vì stored procedure để tương thích với native AOT
            // Equivalent to: UPDATE tbl_xwwl_char SET ... WHERE fld_name = ? AND fld_id = ?
            var result = await _freeSql.Update<tbl_xwwl_char>()
                // Basic character info - Thông tin cơ bản nhân vật
                .Set(c => c.fld_level, characterData.Player_Level)
                .Set(c => c.fld_asc7_anti_qigong, characterData.GetPhanKhiCongCodesbyte)
                .Set(c => c.fld_pinkbag_item, characterData.GetEventBagCodesbyte)
                .Set(c => c.fld_face, characterData.NewCharacterTemplate_CharacterTemplate_byte)
                .Set(c => c.fld_job, characterData.Player_Job)
                .Set(c => c.fld_exp, characterData.CharacterExperience)
                .Set(c => c.fld_zx, characterData.Player_Zx)
                .Set(c => c.fld_job_level, characterData.Player_Job_level)
                // Position - Vị trí
                .Set(c => c.fld_x, characterData.PosX)
                .Set(c => c.fld_y, characterData.PosY)
                .Set(c => c.fld_z, characterData.PosZ)
                .Set(c => c.fld_menow, characterData.MapID)
                // Stats - Chỉ số
                .Set(c => c.fld_money, characterData.Player_Money)
                .Set(c => c.fld_hp, characterData.NhanVat_HP)
                .Set(c => c.fld_mp, characterData.NhanVat_MP)
                .Set(c => c.fld_sp, characterData.NhanVat_SP)
                .Set(c => c.fld_wx, characterData.Player_WuXun)
                .Set(c => c.fld_point, characterData.Player_Qigong_point)
                // Items and equipment - Vật phẩm và trang bị
                .Set(c => c.fld_skills, characterData.GetWgCodesbyte)
                .Set(c => c.fld_wearitem, characterData.GetWEARITEMCodesbyte)
                .Set(c => c.fld_item, characterData.GetFLD_ITEMCodesbyte)
                .Set(c => c.fld_fashion_item, characterData.GetFLD_FASHION_ITEMCodesbyte)
                .Set(c => c.fld_qitem, characterData.GetQuestITEMCodesbyte)
                .Set(c => c.fld_kongfu, characterData.GetFLD_KONGFUCodesbyte)
                .Set(c => c.fld_fight_exp, characterData.Player_ExpErience)
                .Set(c => c.fld_ctime, characterData.GetPersonalMedicinebyte)
                .Set(c => c.fld_doors, characterData.GetThoLinhPhubyte)
                .Set(c => c.fld_quest, characterData.GetNhiemVubyte)
                .Set(c => c.fld_jq, characterData.Character_KhinhCong)
                .Set(c => c.fld_se, characterData.NhanVatThienVaAc)
                .Set(c => c.fld_nametype, characterData.CharacterNameTemplate)
                .Set(c => c.fld_zbver, characterData.EquipmentDataVersion)
                .Set(c => c.fld_zztype, characterData.FLD_LoaiSanXuat)
                .Set(c => c.fld_zzsl, characterData.FLD_TrinhDoSanXuat)
                .Set(c => c.fld_ctimenew, characterData.GetPersonalMedicineNewbyte)
                .Set(c => c.fld_qlname, characterData.FLD_Couple)
                .Set(c => c.fld_love_word, characterData.NhanCuoiKhacChu)
                .Set(c => c.fld_stime, characterData.GetTimeMedicinebyte)
                .Set(c => c.fld_chtime, characterData.GetTitleDrugbyte)
                .Set(c => c.fld_qldu, characterData.FLD_Couple_Love)
                .Set(c => c.fld_qljzname, characterData.FLD_CoupleRing)
                .Set(c => c.fld_marital_status, characterData.GiaiTruQuanHe_Countdown)
                .Set(c => c.fld_married, characterData.WhetherMarried)
                // Special skills - Kỹ năng đặc biệt
                .Set(c => c.fld_thangthienkhicong, characterData.GetThangThienKhiCongCodesbyte)
                .Set(c => c.fld_thangthienvocong, characterData.GetThangThienVoCongCodesbyte)
                .Set(c => c.fld_thangthienlichluyen, characterData.ThangThienLichLuyen_KinhNghiem)
                .Set(c => c.fld_thangthienvocongdiemso, characterData.ThangThienVoCong_DiemSo)
                .Set(c => c.fld_mlz, characterData.FLD_NUMBER_OPEN)
                .Set(c => c.fld_pvp_piont, characterData.FLD_PVP_Piont)
                .Set(c => c.fld_whtb, characterData.Player_Whtb)
                .Set(c => c.fld_fb_time, characterData.RemainingTimeOfTrainingMap)
                .Set(c => c.fld_hd_time, characterData.ActivityMapRemainingTime)
                .Set(c => c.fld_lost_wx, characterData.MatDi_VoHuan)
                .Set(c => c.bangphai_doconghien, characterData.BangPhai_DoCongHien)
                .Set(c => c.fld_titlepoints, characterData.TitlePoints)
                // Additional stats - Chỉ số bổ sung (with special casting for fld_add_at and fld_add_df)
                .Set(c => c.fld_add_hp, characterData.BanThuong_ThemVao_SinhMenh)
                .Set(c => c.fld_add_at, int.Parse(characterData.BanThuong_ThemVao_TanCong ?? "0"))
                .Set(c => c.fld_add_df, int.Parse(characterData.BanThuong_ThemVao_PhongThu ?? "0"))
                .Set(c => c.fld_add_hb, characterData.BanThuong_ThemVao_NeTranh)
                .Set(c => c.fld_add_mp, characterData.BanThuong_ThemVao_NoiCong)
                .Set(c => c.fld_add_mz, characterData.BanThuong_ThemVao_TrungDich)
                .Set(c => c.fld_zs, characterData.NumberOfRebirths)
                .Set(c => c.fld_add_clvc, characterData.BanThuong_ThemVao_CLVC)
                .Set(c => c.fld_add_ptvc, characterData.BanThuong_ThemVao_PTVC)
                .Set(c => c.fld_add_kc, characterData.BanThuong_ThemVao_KC)
                .Set(c => c.fld_rosetitlepoints, characterData.RoseTitlePoints)
                .Set(c => c.fld_config, characterData.ClientSettings)
                .Set(c => c.fld_thannuvocongdiemso, characterData.ThanNuVoCongDiemSo)
                .Set(c => c.fld_get_wx, characterData.NhanVoHuan_MoiNgay)
                .Set(c => c.fld_quest_finish, characterData.GetNhiemVuFinishbyte)
                .Set(c => c.tlc_random_phe, characterData.TheLucChien_PhePhai)
                .Set(c => c.fld_jh_date, characterData.FLD_NgayKiNiemKetHon)
                .Set(c => c.fld_ntcitem, characterData.GetFLD_NTCITEMCodesbyte)
                // WHERE condition - Điều kiện WHERE
                .Where(c => c.fld_name == characterData.CharacterName && c.fld_id == characterData.AccountID)
                .ExecuteAffrowsAsync();

            if (result > 0)
            {
                Logger.Instance.Debug($"✓ Character data saved successfully for {characterData.CharacterName} - Rows affected: {result}");
                return true;
            }
            else
            {
                Logger.Instance.Warning($"No character found to update for {characterData.CharacterName} (Account: {characterData.AccountID})");
                return false;
            }
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Error saving character data for {characterData?.CharacterName}: {ex.Message} {ex.StackTrace}");
            return false;
        }
    }

    /// <summary>
    /// Update personal warehouse data using PostgreSQL function
    /// Cập nhật dữ liệu kho cá nhân sử dụng function PostgreSQL
    /// </summary>
    /// <param name="accountId">Account ID</param>
    /// <param name="characterName">Character name</param>
    /// <param name="money">Warehouse money</param>
    /// <param name="itemData">Item data as byte array</param>
    /// <returns>True if successful, false if failed</returns>
    public static async Task<bool> UpdatePersonalWarehouseAsync(string accountId, string characterName, string money, byte[] itemData)
    {
        try
        {
            if (_freeSql == null)
            {
                Logger.Instance.Error("FreeSql instance is not initialized");
                return false;
            }

            if (string.IsNullOrEmpty(accountId) || string.IsNullOrEmpty(characterName))
            {
                Logger.Instance.Error("Account ID or Character Name is null or empty");
                return false;
            }

            // Use FreeSql ORM Update method instead of stored procedure for native AOT compatibility
            // Sử dụng phương thức Update của FreeSql ORM thay vì stored procedure để tương thích với native AOT
            // Equivalent to: UPDATE tbl_xwwl_warehouse SET fld_money = ?, fld_item = ? WHERE fld_name = ? AND fld_id = ?
            var result = await _freeSql.Update<tbl_xwwl_warehouse>()
                .Set(w => w.fld_money, money ?? "0")
                .Set(w => w.fld_item, itemData ?? new byte[0])
                .Where(w => w.fld_name == characterName && w.fld_id == accountId)
                .ExecuteAffrowsAsync();

            if (result > 0)
            {
                Logger.Instance.Debug($"✓ Personal warehouse updated successfully for {characterName} - Rows affected: {result}");
                return true;
            }
            else
            {
                Logger.Instance.Warning($"No personal warehouse found to update for {characterName} (Account: {accountId})");
                return false;
            }
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Error updating personal warehouse for {characterName}: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Update comprehensive/public warehouse data using PostgreSQL function
    /// Cập nhật dữ liệu kho tổng hợp/công cộng sử dụng function PostgreSQL
    /// </summary>
    /// <param name="accountId">Account ID</param>
    /// <param name="money">Warehouse money</param>
    /// <param name="itemData">Item data as byte array</param>
    /// <param name="timeData">Time data as byte array</param>
    /// <param name="version">Equipment data version</param>
    /// <returns>True if successful, false if failed</returns>
    public static async Task<bool> UpdateComprehensiveWarehouseAsync(string accountId, string money, byte[] itemData, byte[] timeData, int version)
    {
        try
        {
            if (_freeSql == null)
            {
                Logger.Instance.Error("FreeSql instance is not initialized");
                return false;
            }

            if (string.IsNullOrEmpty(accountId))
            {
                Logger.Instance.Error("Account ID is null or empty");
                return false;
            }

            // Use FreeSql ORM Update method instead of stored procedure for native AOT compatibility
            // Sử dụng phương thức Update của FreeSql ORM thay vì stored procedure để tương thích với native AOT
            // Equivalent to: UPDATE tbl_xwwl_publicwarehouse SET ... WHERE fld_id = ?
            var result = await _freeSql.Update<tbl_xwwl_publicwarehouse>()
                .Set(w => w.fld_money, money ?? "0")
                .Set(w => w.fld_item, itemData ?? new byte[0])
                .Set(w => w.fld_itime, timeData ?? new byte[0])
                .Set(w => w.fld_zbver, version)
                .Where(w => w.fld_id == accountId)
                .ExecuteAffrowsAsync();

            if (result > 0)
            {
                Logger.Instance.Debug($"✓ Comprehensive warehouse updated successfully for account {accountId} - Rows affected: {result}");
                return true;
            }
            else
            {
                Logger.Instance.Warning($"No comprehensive warehouse found to update for account {accountId}");
                return false;
            }
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Error updating comprehensive warehouse for account {accountId}: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Update spirit beast/pet data using PostgreSQL function
    /// Cập nhật dữ liệu linh thú/thú cưng sử dụng function PostgreSQL
    /// </summary>
    /// <param name="id">Spirit beast ID</param>
    /// <param name="name">Spirit beast name</param>
    /// <param name="level">Level</param>
    /// <param name="zcd">ZCD value</param>
    /// <param name="job">Job type</param>
    /// <param name="jobLevel">Job level</param>
    /// <param name="exp">Experience points</param>
    /// <param name="hp">Health points</param>
    /// <param name="mp">Mana points</param>
    /// <param name="wearItemData">Equipment data</param>
    /// <param name="itemData">Item data</param>
    /// <param name="kongfuData">Kungfu data</param>
    /// <param name="bs">BS value</param>
    /// <param name="magic1">Magic 1</param>
    /// <param name="magic2">Magic 2</param>
    /// <param name="magic3">Magic 3</param>
    /// <param name="magic4">Magic 4</param>
    /// <param name="magic5">Magic 5</param>
    /// <returns>True if successful, false if failed</returns>
    public static async Task<bool> UpdateSpiritBeastDataAsync(
        long id, string name, int level, int zcd, int job, int jobLevel,
        string exp, int hp, int mp, byte[] wearItemData, byte[] itemData,
        byte[] kongfuData, int bs, int magic1, int magic2, int magic3, int magic4, int magic5)
    {
        try
        {
            if (_freeSql == null)
            {
                Logger.Instance.Error("FreeSql instance is not initialized");
                return false;
            }

            if (string.IsNullOrEmpty(name))
            {
                Logger.Instance.Error("Spirit beast name is null or empty");
                return false;
            }

            // Use FreeSql ORM Update method instead of stored procedure for native AOT compatibility
            // Sử dụng phương thức Update của FreeSql ORM thay vì stored procedure để tương thích với native AOT
            // Equivalent to: UPDATE tbl_xwwl_cw SET ... WHERE itmeid = CAST(p_id AS INTEGER)
            var result = await _freeSql.Update<tbl_xwwl_cw>()
                .Set(c => c.fld_level, level)
                .Set(c => c.fld_zcd, zcd)
                .Set(c => c.fld_job, job)
                .Set(c => c.fld_job_level, jobLevel)
                .Set(c => c.fld_exp, exp ?? "0")
                .Set(c => c.fld_hp, hp)
                .Set(c => c.fld_bs, bs)
                .Set(c => c.fld_mp, mp)
                .Set(c => c.fld_wearitem, wearItemData ?? new byte[0])
                .Set(c => c.fld_item, itemData ?? new byte[0])
                .Set(c => c.fld_kongfu, kongfuData ?? new byte[0])
                .Set(c => c.name, name)
                .Set(c => c.fld_magic1, magic1)
                .Set(c => c.fld_magic2, magic2)
                .Set(c => c.fld_magic3, magic3)
                .Set(c => c.fld_magic4, magic4)
                .Set(c => c.fld_magic5, magic5)
                .Where(c => c.itmeid == id) // WHERE itmeid = CAST(p_id AS INTEGER)
                .ExecuteAffrowsAsync();

            if (result > 0)
            {
                Logger.Instance.Debug($"✓ Spirit beast data updated successfully for {name} (ID: {id}) - Rows affected: {result}");
                return true;
            }
            else
            {
                Logger.Instance.Warning($"No spirit beast found to update for ID: {id}");
                return false;
            }
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Error updating spirit beast data for {name} (ID: {id}): {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Update mentoring/master-student data using PostgreSQL function
    /// Cập nhật dữ liệu sư đồ/thầy trò sử dụng function PostgreSQL
    /// </summary>
    /// <param name="name">Character name</param>
    /// <param name="level">Teacher level</param>
    /// <param name="stLevel">Student level</param>
    /// <param name="stYhd">Student YHD value</param>
    /// <param name="stWg1">Student WG1 value</param>
    /// <param name="stWg2">Student WG2 value</param>
    /// <param name="stWg3">Student WG3 value</param>
    /// <returns>True if successful, false if failed</returns>
    public static async Task<bool> UpdateMentoringDataAsync(
        string name, int level, int stLevel, int stYhd, int stWg1, int stWg2, int stWg3)
    {
        try
        {
            if (_freeSql == null)
            {
                Logger.Instance.Error("FreeSql instance is not initialized");
                return false;
            }

            if (string.IsNullOrEmpty(name))
            {
                Logger.Instance.Error("Character name is null or empty");
                return false;
            }

            // Use FreeSql ORM Update method instead of stored procedure for native AOT compatibility
            // Sử dụng phương thức Update của FreeSql ORM thay vì stored procedure để tương thích với native AOT
            // Equivalent to: UPDATE tbl_sudosolieu SET ... WHERE fld_tname = ?
            var result = await _freeSql.Update<tbl_sudosolieu>()
                .Set(s => s.fld_tlevel, level)
                .Set(s => s.fld_stlevel, stLevel)
                .Set(s => s.fld_styhd, stYhd)
                .Set(s => s.fld_stwg1, stWg1)
                .Set(s => s.fld_stwg2, stWg2)
                .Set(s => s.fld_stwg3, stWg3)
                .Where(s => s.fld_tname == name)
                .ExecuteAffrowsAsync();

            if (result > 0)
            {
                Logger.Instance.Debug($"✓ Mentoring data updated successfully for {name} - Rows affected: {result}");
                return true;
            }
            else
            {
                Logger.Instance.Warning($"No mentoring data found to update for {name}");
                return false;
            }
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Error updating mentoring data for {name}: {ex.Message}");
            return false;
        }
    }

    #endregion



    #region EventTop - Faction War Event System

    /// <summary>
    /// Save EventTop results for faction war event with date-based storage
    /// Lưu kết quả EventTop cho sự kiện thế lực chiến theo ngày
    /// </summary>
    /// <param name="eventTopData">Dictionary containing EventTop data from World.EventTop</param>
    /// <param name="chinhPhaiScore">Chinh Phai faction score</param>
    /// <param name="taPhaiScore">Ta Phai faction score</param>
    /// <returns>True if successful, false if failed</returns>
    public static async Task<bool> SaveEventTopResults(Dictionary<string, EventTopClass> eventTopData, int chinhPhaiScore, int taPhaiScore)
    {
        try
        {
            if (_freeSql == null)
            {
                Logger.Instance.Error("FreeSql instance is not initialized");
                return false;
            }

            if (eventTopData == null || eventTopData.Count == 0)
            {
                Logger.Instance.Warning("No EventTop data to save");
                return true; // Not an error, just no data
            }

            var today = DateTime.Now.Date;
            var eventTopRecords = new List<eventtop>();

            // Convert EventTopClass data to eventtop entities
            foreach (var kvp in eventTopData)
            {
                var eventData = kvp.Value;

                // Check if record already exists for this player today
                var existingRecord = await _freeSql
                    .Select<eventtop>()
                    .Where(a => a.tennhanvat == eventData.TenNhanVat && a.createdat == today)
                    .FirstAsync();

                if (existingRecord != null)
                {
                    // Update existing record
                    await _freeSql
                        .Update<eventtop>()
                        .Set(a => a.bangphai == eventData.BangPhai)
                        .Set(a => a.theluc == eventData.TheLuc)
                        .Set(a => a.dangcap == eventData.DangCap)
                        .Set(a => a.gietnguoisoluong == eventData.GietNguoiSoLuong)
                        .Set(a => a.tuvongsoluong == eventData.TuVongSoLuong)
                        .Set(a => a.diem_chinhphai == chinhPhaiScore)
                        .Set(a => a.diem_taphai == taPhaiScore)
                        .Where(a => a.id == existingRecord.id)
                        .ExecuteAffrowsAsync();
                }
                else
                {
                    // Create new record
                    eventTopRecords.Add(new eventtop
                    {
                        tennhanvat = eventData.TenNhanVat,
                        bangphai = eventData.BangPhai,
                        theluc = eventData.TheLuc,
                        dangcap = eventData.DangCap,
                        gietnguoisoluong = eventData.GietNguoiSoLuong,
                        tuvongsoluong = eventData.TuVongSoLuong,
                        diem_chinhphai = chinhPhaiScore,
                        diem_taphai = taPhaiScore,
                        createdat = today
                    });
                }
            }

            // Batch insert new records if any
            if (eventTopRecords.Count > 0)
            {
                var insertedRows = await _freeSql
                    .Insert(eventTopRecords)
                    .ExecuteAffrowsAsync();

                Logger.Instance.Info($"✓ Inserted {insertedRows} new EventTop records for {today:yyyy-MM-dd}");
            }

            Logger.Instance.Info($"✓ EventTop results saved successfully for {today:yyyy-MM-dd} - Total players: {eventTopData.Count}");
            return true;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Error saving EventTop results: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Get EventTop records for a specific date
    /// Lấy danh sách EventTop theo ngày cụ thể
    /// </summary>
    /// <param name="date">Date to query (default: today)</param>
    /// <returns>List of EventTop records</returns>
    public static async Task<List<eventtop>> GetEventTopByDate(DateTime? date = null)
    {
        try
        {
            if (_freeSql == null)
            {
                Logger.Instance.Error("FreeSql instance is not initialized");
                return new List<eventtop>();
            }

            var queryDate = date?.Date ?? DateTime.Now.Date;

            var records = await _freeSql
                .Select<eventtop>()
                .Where(a => a.createdat == queryDate)
                .OrderByDescending(a => a.gietnguoisoluong)
                .OrderByDescending(a => a.dangcap)
                .ToListAsync();

            Logger.Instance.Debug($"✓ Retrieved {records.Count} EventTop records for {queryDate:yyyy-MM-dd}");
            return records;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Error retrieving EventTop records: {ex.Message}");
            return new List<eventtop>();
        }
    }


    /// <summary>
    /// Get top players from EventTop for a specific date with ranking
    /// Lấy danh sách top người chơi từ EventTop theo ngày với xếp hạng
    /// </summary>
    /// <param name="date">Date to query (default: today)</param>
    /// <param name="topCount">Number of top players to return (default: 20)</param>
    /// <returns>List of top EventTop records with ranking</returns>
    public static async Task<List<eventtop>> GetTopEventTopPlayers(DateTime? date = null, int topCount = 20)
    {
        try
        {
            if (_freeSql == null)
            {
                Logger.Instance.Error("FreeSql instance is not initialized");
                return new List<eventtop>();
            }

            var queryDate = date?.Date ?? DateTime.Now.Date;

            var topPlayers = await _freeSql
                .Select<eventtop>()
                .Where(a => a.createdat == queryDate)
                .OrderByDescending(a => a.gietnguoisoluong)
                .OrderByDescending(a => a.dangcap)
                .Take(topCount)
                .ToListAsync();

            Logger.Instance.Debug($"✓ Retrieved top {topPlayers.Count} EventTop players for {queryDate:yyyy-MM-dd}");
            return topPlayers;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Error retrieving top EventTop players: {ex.Message}");
            return new List<eventtop>();
        }
    }

    /// <summary>
    /// Save EventTop DCH results for Dai Chien Hon event with date-based storage
    /// Lưu kết quả EventTop DCH cho sự kiện Đại Chiến Hồn theo ngày
    /// </summary>
    /// <param name="eventTopDCHData">Dictionary containing EventTopDCH data from World.EventTopDCH</param>
    /// <param name="chinhPhaiScore">Chinh Phai faction score</param>
    /// <param name="taPhaiScore">Ta Phai faction score</param>
    /// <returns>True if successful, false if failed</returns>
    public static async Task<bool> SaveEventTopDCHResults(Dictionary<string, EventTopDCHClass> eventTopDCHData, int chinhPhaiScore, int taPhaiScore)
    {
        try
        {
            if (_freeSql == null)
            {
                Logger.Instance.Error("FreeSql instance is not initialized");
                return false;
            }

            if (eventTopDCHData == null || eventTopDCHData.Count == 0)
            {
                Logger.Instance.Warning("No EventTopDCH data to save");
                return true; // Not an error, just no data
            }

            var today = DateTime.Now.Date;
            var eventTopDCHRecords = new List<eventtop_dch>();

            // Convert EventTopDCHClass data to eventtop_dch entities
            foreach (var kvp in eventTopDCHData)
            {
                var eventData = kvp.Value;

                // Check if record already exists for this player today
                var existingRecord = await _freeSql
                    .Select<eventtop_dch>()
                    .Where(a => a.tennhanvat == eventData.TenNhanVat && a.createdat == today)
                    .FirstAsync();

                if (existingRecord != null)
                {
                    // Update existing record
                    await _freeSql
                        .Update<eventtop_dch>()
                        .Set(a => a.bangphai == eventData.BangPhai)
                        .Set(a => a.theluc == eventData.TheLuc)
                        .Set(a => a.dangcap == eventData.DangCap)
                        .Set(a => a.gietnguoisoluong == eventData.GietNguoiSoLuong)
                        .Set(a => a.tuvongsoluong == eventData.TuVongSoLuong)
                        .Set(a => a.dame_tru == eventData.Dame_Tru)
                        .Set(a => a.hoptacgietnguoi == eventData.HopTacGietNguoi)
                        .Set(a => a.diem_dch_chinhphai == chinhPhaiScore)
                        .Set(a => a.diem_dch_taphai == taPhaiScore)
                        .Where(a => a.id == existingRecord.id)
                        .ExecuteAffrowsAsync();
                }
                else
                {
                    // Create new record
                    eventTopDCHRecords.Add(new eventtop_dch
                    {
                        tennhanvat = eventData.TenNhanVat,
                        bangphai = eventData.BangPhai,
                        theluc = eventData.TheLuc,
                        dangcap = eventData.DangCap,
                        gietnguoisoluong = eventData.GietNguoiSoLuong,
                        tuvongsoluong = eventData.TuVongSoLuong,
                        dame_tru = eventData.Dame_Tru,
                        hoptacgietnguoi = eventData.HopTacGietNguoi,
                        diem_dch_chinhphai = chinhPhaiScore,
                        diem_dch_taphai = taPhaiScore,
                        createdat = today
                    });
                }
            }

            // Batch insert new records if any
            if (eventTopDCHRecords.Count > 0)
            {
                var insertedRows = await _freeSql
                    .Insert(eventTopDCHRecords)
                    .ExecuteAffrowsAsync();

                Logger.Instance.Info($"✓ Inserted {insertedRows} new EventTopDCH records for {today:yyyy-MM-dd}");
            }

            Logger.Instance.Info($"✓ EventTopDCH results saved successfully for {today:yyyy-MM-dd} - Total players: {eventTopDCHData.Count}");
            return true;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Error saving EventTopDCH results: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Get EventTop DCH records for a specific date
    /// Lấy danh sách EventTop DCH theo ngày cụ thể
    /// </summary>
    /// <param name="date">Date to query (default: today)</param>
    /// <returns>List of EventTop DCH records</returns>
    public static async Task<List<eventtop_dch>> GetEventTopDCHByDate(DateTime? date = null)
    {
        try
        {
            if (_freeSql == null)
            {
                Logger.Instance.Error("FreeSql instance is not initialized");
                return new List<eventtop_dch>();
            }

            var queryDate = date?.Date ?? DateTime.Now.Date;

            var records = await _freeSql
                .Select<eventtop_dch>()
                .Where(a => a.createdat == queryDate)
                .OrderByDescending(a => a.dame_tru)
                .OrderByDescending(a => a.gietnguoisoluong)
                .OrderByDescending(a => a.dangcap)
                .ToListAsync();

            Logger.Instance.Debug($"✓ Retrieved {records.Count} EventTopDCH records for {queryDate:yyyy-MM-dd}");
            return records;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Error retrieving EventTopDCH records: {ex.Message}");
            return new List<eventtop_dch>();
        }
    }

    /// <summary>
    /// Get top players from EventTop DCH for a specific date with ranking
    /// Lấy danh sách top người chơi từ EventTop DCH theo ngày với xếp hạng
    /// </summary>
    /// <param name="date">Date to query (default: today)</param>
    /// <param name="topCount">Number of top players to return (default: 20)</param>
    /// <returns>List of top EventTop DCH records with ranking</returns>
    public static async Task<List<eventtop_dch>> GetTopEventTopDCHPlayers(DateTime? date = null, int topCount = 20)
    {
        try
        {
            if (_freeSql == null)
            {
                Logger.Instance.Error("FreeSql instance is not initialized");
                return new List<eventtop_dch>();
            }

            var queryDate = date?.Date ?? DateTime.Now.Date;

            var topPlayers = await _freeSql
                .Select<eventtop_dch>()
                .Where(a => a.createdat == queryDate)
                .OrderByDescending(a => a.dame_tru)
                .OrderByDescending(a => a.gietnguoisoluong)
                .OrderByDescending(a => a.dangcap)
                .Take(topCount)
                .ToListAsync();

            Logger.Instance.Debug($"✓ Retrieved top {topPlayers.Count} EventTopDCH players for {queryDate:yyyy-MM-dd}");
            return topPlayers;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Error retrieving top EventTopDCH players: {ex.Message}");
            return new List<eventtop_dch>();
        }
    }

    /// <summary>
    /// Clean up old EventTop DCH records (older than specified days)
    /// Dọn dẹp các bản ghi EventTop DCH cũ (cũ hơn số ngày chỉ định)
    /// </summary>
    /// <param name="daysToKeep">Number of days to keep (default: 30)</param>
    /// <returns>Number of records deleted</returns>
    public static async Task<int> CleanupOldEventTopDCHRecords(int daysToKeep = 30)
    {
        try
        {
            if (_freeSql == null)
            {
                Logger.Instance.Error("FreeSql instance is not initialized");
                return 0;
            }

            var cutoffDate = DateTime.Now.Date.AddDays(-daysToKeep);

            var deletedRows = await _freeSql
                .Delete<eventtop_dch>()
                .Where(a => a.createdat < cutoffDate)
                .ExecuteAffrowsAsync();

            if (deletedRows > 0)
            {
                Logger.Instance.Info($"✓ Cleaned up {deletedRows} old EventTopDCH records (older than {cutoffDate:yyyy-MM-dd})");
            }

            return deletedRows;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Error cleaning up old EventTopDCH records: {ex.Message}");
            return 0;
        }
    }

    /// <summary>
    /// Get player ranking and reward information for DCH event
    /// Lấy thông tin xếp hạng và phần thưởng cho sự kiện DCH
    /// </summary>
    /// <param name="playerName">Player name to check ranking</param>
    /// <param name="date">Date to query (default: today)</param>
    /// <returns>Player ranking info with reward details, null if not in top 20</returns>
    public static async Task<DCHPlayerRankingInfo> GetDCHPlayerRanking(string playerName, DateTime? date = null)
    {
        try
        {
            if (_freeSql == null)
            {
                Logger.Instance.Error("FreeSql instance is not initialized");
                return null;
            }

            if (string.IsNullOrEmpty(playerName))
            {
                Logger.Instance.Warning("Player name is null or empty");
                return null;
            }

            var queryDate = date?.Date ?? DateTime.Now.Date;

            // Get all players ordered by dame_tru and find player rank
            var allPlayers = await _freeSql
                .Select<eventtop_dch>()
                .Where(a => a.createdat == queryDate)
                .OrderByDescending(a => a.dame_tru)
                .OrderByDescending(a => a.gietnguoisoluong)
                .OrderByDescending(a => a.dangcap)
                .ToListAsync();

            var playerIndex = allPlayers.FindIndex(x => x.tennhanvat == playerName);

            if (playerIndex == -1 || playerIndex >= 20)
            {
                return null; // Player not found or not in top 20
            }

            var playerRanking = allPlayers[playerIndex];
            var rank = playerIndex + 1; // Convert 0-based index to 1-based rank

            // Calculate reward based on ranking
            var rewardInfo = CalculateDCHReward(rank);

            return new DCHPlayerRankingInfo
            {
                PlayerName = playerName,
                Rank = rank,
                DameTru = playerRanking.dame_tru ?? 0,
                GietNguoiSoLuong = playerRanking.gietnguoisoluong ?? 0,
                DangCap = playerRanking.dangcap ?? 0,
                WuXunReward = rewardInfo.WuXunReward,
                ItemReward = rewardInfo.ItemReward,
                RewardMessage = rewardInfo.RewardMessage
            };
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Error getting DCH player ranking for {playerName}: {ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// Calculate DCH reward based on player ranking
    /// Tính toán phần thưởng DCH dựa trên xếp hạng
    /// </summary>
    /// <param name="rank">Player rank (1-20)</param>
    /// <returns>Reward information</returns>
    private static DCHRewardInfo CalculateDCHReward(int rank)
    {
        var rewardInfo = new DCHRewardInfo
        {
            ItemReward = 1008000085 // Hộp Thần Khí - same for all top 20
        };

        // Calculate WuXun reward based on rank
        switch (rank)
        {
            case 1: rewardInfo.WuXunReward = 2000; break;
            case 2: rewardInfo.WuXunReward = 1900; break;
            case 3: rewardInfo.WuXunReward = 1800; break;
            case 4: rewardInfo.WuXunReward = 1700; break;
            case 5: rewardInfo.WuXunReward = 1600; break;
            case 6: rewardInfo.WuXunReward = 1500; break;
            case 7: rewardInfo.WuXunReward = 1400; break;
            case 8: rewardInfo.WuXunReward = 1300; break;
            case 9: rewardInfo.WuXunReward = 1200; break;
            case 10: rewardInfo.WuXunReward = 1100; break;
            case 11: rewardInfo.WuXunReward = 1000; break;
            case 12: rewardInfo.WuXunReward = 900; break;
            case 13: rewardInfo.WuXunReward = 800; break;
            case 14: rewardInfo.WuXunReward = 700; break;
            case 15: rewardInfo.WuXunReward = 600; break;
            case 16: rewardInfo.WuXunReward = 500; break;
            case 17: rewardInfo.WuXunReward = 400; break;
            case 18: rewardInfo.WuXunReward = 300; break;
            case 19: rewardInfo.WuXunReward = 200; break;
            case 20: rewardInfo.WuXunReward = 100; break;
            default: rewardInfo.WuXunReward = 0; break;
        }

        rewardInfo.RewardMessage = $"Phần thưởng Top {rank}: [{rewardInfo.WuXunReward}] Võ Huân!";
        return rewardInfo;
    }

    /// <summary>
    /// Get top 20 DCH players with their rankings and rewards
    /// Lấy top 20 người chơi DCH với xếp hạng và phần thưởng
    /// </summary>
    /// <param name="date">Date to query (default: today)</param>
    /// <returns>List of top 20 players with ranking and reward info</returns>
    public static async Task<List<DCHPlayerRankingInfo>> GetTop20DCHPlayersWithRewards(DateTime? date = null)
    {
        try
        {
            if (_freeSql == null)
            {
                Logger.Instance.Error("FreeSql instance is not initialized");
                return new List<DCHPlayerRankingInfo>();
            }

            var queryDate = date?.Date ?? DateTime.Now.Date;

            // Get top 20 players ordered by dame_tru
            var topPlayers = await _freeSql
                .Select<eventtop_dch>()
                .Where(a => a.createdat == queryDate)
                .OrderByDescending(a => a.dame_tru)
                .OrderByDescending(a => a.gietnguoisoluong)
                .OrderByDescending(a => a.dangcap)
                .Take(20)
                .ToListAsync();

            var result = new List<DCHPlayerRankingInfo>();

            for (int i = 0; i < topPlayers.Count; i++)
            {
                var player = topPlayers[i];
                var rank = i + 1; // Convert 0-based index to 1-based rank
                var rewardInfo = CalculateDCHReward(rank);

                result.Add(new DCHPlayerRankingInfo
                {
                    PlayerName = player.tennhanvat,
                    Rank = rank,
                    DameTru = player.dame_tru ?? 0,
                    GietNguoiSoLuong = player.gietnguoisoluong ?? 0,
                    DangCap = player.dangcap ?? 0,
                    WuXunReward = rewardInfo.WuXunReward,
                    ItemReward = rewardInfo.ItemReward,
                    RewardMessage = rewardInfo.RewardMessage
                });
            }

            Logger.Instance.Debug($"✓ Retrieved top {result.Count} DCH players with rewards for {queryDate:yyyy-MM-dd}");
            return result;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Error getting top 20 DCH players with rewards: {ex.Message}");
            return new List<DCHPlayerRankingInfo>();
        }
    }

    #endregion

    #region Offline Character Loading

    /// <summary>
    /// Load offline characters for off-attack system
    /// Tải danh sách nhân vật offline cho hệ thống tấn công offline
    /// </summary>
    /// <param name="limit">Maximum number of characters to load (default: 500)</param>
    /// <returns>List of offline characters ordered by level descending</returns>
    public static async Task<List<tbl_xwwl_char>> LoadOfflineCharacters(int limit = 500)
    {
        try
        {
            if (_freeSql == null)
            {
                Logger.Instance.Error("FreeSql instance is not initialized");
                return new List<tbl_xwwl_char>();
            }

            // Get offline characters ordered by level (highest first)
            var offlineChars = await _freeSql
                .Select<tbl_xwwl_char>()
                .Where(a => a.fld_id != "1" && a.fld_id != "2") // Exclude system accounts
                .OrderByDescending(a => a.fld_level)
                .Take(limit)
                .ToListAsync();

            Logger.Instance.Info($"✓ Loaded {offlineChars.Count} offline characters from database");
            return offlineChars;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Error loading offline characters: {ex.Message}");
            return new List<tbl_xwwl_char>();
        }
    }

    /// <summary>
    /// Data structure for offline character with account info
    /// Cấu trúc dữ liệu cho nhân vật offline kèm thông tin tài khoản
    /// </summary>
    public class OfflineCharacterData
    {
        public tbl_xwwl_char Character { get; set; }
        public string Password { get; set; }
        public int RxPoint { get; set; }
        public int RxPointX { get; set; }
        public int Coin { get; set; }
        public int Vip { get; set; }
        public DateTime? VipTime { get; set; }
        public int Sex { get; set; }
        public string LastLoginIP { get; set; }
        public string MachineID { get; set; }
        public string SafeWord { get; set; }
        public bool IsOnline { get; set; }
    }

    /// <summary>
    /// Load offline characters with account data for off-attack system (using 2 separate queries)
    /// Tải nhân vật offline kèm dữ liệu tài khoản cho hệ thống tấn công offline (sử dụng 2 query riêng biệt)
    /// </summary>
    /// <param name="limit">Maximum number of characters to load (default: 500)</param>
    /// <returns>List of offline character data with account info</returns>
    public static async Task<List<OfflineCharacterData>> LoadOfflineCharactersWithAccountData(int limit = 500)
    {
        try
        {
            if (_freeSql == null)
            {
                Logger.Instance.Error("FreeSql instance is not initialized");
                return new List<OfflineCharacterData>();
            }

            // Step 1: Load characters ordered by level (highest first)
            var characters = await _freeSql
                .Select<tbl_xwwl_char>()
                .Where(a => a.fld_id != "1" && a.fld_id != "2") // Exclude system accounts
                .OrderByDescending(a => a.fld_level)
                .Take(limit)
                .ToListAsync();

            if (characters.Count == 0)
            {
                Logger.Instance.Info("No characters found to load");
                return new List<OfflineCharacterData>();
            }

            // Step 2: Get unique account IDs
            var accountIds = characters.Select(c => c.fld_id).Distinct().ToList();

            // Note: This would need AccountDb connection to get account data
            // For now, we'll create placeholder data structure
            var result = new List<OfflineCharacterData>();

            foreach (var character in characters)
            {
                result.Add(new OfflineCharacterData
                {
                    Character = character,
                    // Placeholder values - these would come from AccountDb query
                    Password = "placeholder",
                    RxPoint = 0,
                    RxPointX = 0,
                    Coin = 0,
                    Vip = 0,
                    VipTime = null,
                    Sex = 0,
                    LastLoginIP = "0.0.0.0",
                    MachineID = "placeholder",
                    SafeWord = "placeholder",
                    IsOnline = false
                });
            }

            Logger.Instance.Info($"✓ Loaded {result.Count} offline characters with account data");
            return result;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Error loading offline characters with account data: {ex.Message}");
            return new List<OfflineCharacterData>();
        }
    }

    #endregion

    #region Player

    public static async void UpdateRoseTop(string characterName, int outnum)
    {
        try
        {
            await _freeSql
            .Update<tbl_xwwl_rosetop>()
            .Set(a => a.fld_outnum == outnum)
            .Where(a => a.fld_name == characterName)
            .ExecuteAffrowsAsync();
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Error Updating rosetop " + ex.Message);
        }
    }

    public static async Task<bool> InsertRoseTop(string characterName, int innum, int outnum, int faction)
    {
        try
        {
            await _freeSql
            .Insert(new tbl_xwwl_rosetop
            {
                fld_name = characterName,
                fld_innum = innum,
                fld_outnum = outnum,
                fld_zx = faction
            })
            .ExecuteAffrowsAsync();
            return true;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Error Inserting rosetop " + ex.Message);
            return false;
        }
    }


    public static async Task<tbl_xwwl_rosetop> FindRoseTop(string characterName)
    {
        try
        {
            return await _freeSql
            .Select<tbl_xwwl_rosetop>()
            .Where(a => a.fld_name == characterName)
            .OrderByDescending(a => a.fld_innum)
            .FirstAsync();
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Error Finding rosetop " + ex.Message);
            return null;
        }
    }
    public static async Task<List<tbl_xwwl_rosetop>> FindRoseTop(string characterName, int faction)
    {
        try
        {
            return await _freeSql
            .Select<tbl_xwwl_rosetop>()
            .Where(a => a.fld_name == characterName)
            .Where(a => a.fld_zx == faction)
            .OrderByDescending(a => a.fld_innum)
            .ToListAsync();
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Error Finding rosetop " + ex.Message);
            return null;
        }
    }
    public static async Task<List<tbl_xwwl_rosetop>> FindRoseTops()
    {
        try
        {
            return await _freeSql
            .Select<tbl_xwwl_rosetop>()
            .OrderByDescending(a => a.fld_innum)
            .ToListAsync();
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Error Finding rosetop " + ex.Message);
            return null;
        }
    }

    public static async Task<List<tbl_xwwl_char>> FindAllCharactersByAccount(string accountId)
    {
        try
        {
            return await _freeSql
            .Select<tbl_xwwl_char>()
            .Where(a => a.fld_id == accountId)
            .OrderBy(a => a.fld_index)
            .ToListAsync();
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Error Finding all characters by account " + ex.Message);
            return null;
        }
    }

    /// <summary>
    /// Delete character and all related data safely with transaction
    /// Xóa nhân vật và tất cả dữ liệu liên quan một cách an toàn với transaction
    /// </summary>
    /// <param name="charactername">Character name - Tên nhân vật</param>
    /// <param name="accountId">Account ID - ID tài khoản</param>
    /// <returns>True if successful, false if failed</returns>
    public static bool DeleteCharacter(string charactername, string accountId)
    {
        try
        {
            if (string.IsNullOrEmpty(charactername) || string.IsNullOrEmpty(accountId))
            {
                Logger.Instance.Error("DeleteCharacter: Character name or account ID cannot be null or empty");
                return false;
            }

            Logger.Instance.Info($"Starting character deletion: {charactername} (Account: {accountId})");

            var deletedRecords = 0;

            // Use transaction to ensure data consistency
            _freeSql.Transaction(async () =>
            {
                // 1. Delete character main data
                var charDeleted = await _freeSql
                    .Delete<tbl_xwwl_char>()
                    .Where(a => a.fld_name == charactername && a.fld_id == accountId)
                    .ExecuteAffrowsAsync();
                deletedRecords += charDeleted;
                Logger.Instance.Debug($"Deleted character data: {charDeleted} records");

                // 2. Delete warehouse data
                var warehouseDeleted = await _freeSql
                    .Delete<tbl_xwwl_warehouse>()
                    .Where(a => a.fld_name == charactername && a.fld_id == accountId)
                    .ExecuteAffrowsAsync();
                deletedRecords += warehouseDeleted;
                Logger.Instance.Debug($"Deleted warehouse data: {warehouseDeleted} records");

                // 3. Delete guild membership
                var guildDeleted = await _freeSql
                    .Delete<tbl_xwwl_guildmember>()
                    .Where(a => a.fld_name == charactername)
                    .ExecuteAffrowsAsync();
                deletedRecords += guildDeleted;
                Logger.Instance.Debug($"Deleted guild membership: {guildDeleted} records");

                // 4. Delete mail system data (received mails)
                var mailDeleted = await _freeSql
                    .Delete<tbl_truyenthuhethong>()
                    .Where(a => a.nguoinhanthu_nhatvatten == charactername)
                    .ExecuteAffrowsAsync();
                deletedRecords += mailDeleted;
                Logger.Instance.Debug($"Deleted received mails: {mailDeleted} records");
            });

            Logger.Instance.Info($"✓ Character deletion completed: {charactername} - Total records deleted: {deletedRecords}");
            return true;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Error deleting character {charactername}: {ex.Message}");
            Logger.Instance.Error($"Stack trace: {ex.StackTrace}");
            return false;
        }
    }

    public static async Task UpdateFQID(string characterName, string accountId, string fqid)
    {
        try
        {
            await _freeSql
            .Update<tbl_xwwl_char>()
            .Set(a => a.fld_fqid == fqid)
            .Where(a => a.fld_name == characterName && a.fld_id == accountId)
            .ExecuteAffrowsAsync();
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Error Updating FQID " + ex.Message);
        }
    }


    public static async Task UpdateFactionWar(string characterName, string accountId, string faction)
    {
        try
        {
            await _freeSql
            .Update<tbl_xwwl_char>()
            .Set(a => a.tlc_random_phe == faction)
            .Where(a => a.fld_name == characterName && a.fld_id == accountId)
            .ExecuteAffrowsAsync();
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Error Updating faction war " + ex.Message);
        }
    }
    public static async Task<tbl_xwwl_char> FindCharacter(string characterName)
    {
        try
        {
            return await _freeSql
            .Select<tbl_xwwl_char>()
            .Where(a => a.fld_name == characterName)
            .FirstAsync();
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Error Finding character " + ex.Message);
            return null;
        }
    }

    public static async Task<tbl_xwwl_char> FindCharacter(string characterName, string accountId)
    {
        try
        {
            return await _freeSql
            .Select<tbl_xwwl_char>()
            .Where(a => a.fld_name == characterName && a.fld_id == accountId)
            .FirstAsync();
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Error Finding character " + ex.Message);
            return null;
        }
    }

    public static async Task<tbl_xwwl_char> FindCharacter(string accountId, int index)
    {
        try
        {
            return await _freeSql
            .Select<tbl_xwwl_char>()
            .Where(a => a.fld_id == accountId && a.fld_index == index)
            .FirstAsync();
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Error Finding character " + ex.Message);
            return null;
        }
    }

    /// <summary>
    /// Change character name with transaction rollback support
    /// Đổi tên nhân vật với hỗ trợ rollback transaction
    /// </summary>
    /// <param name="accountId">Account ID - ID tài khoản</param>
    /// <param name="oldName">Old character name - Tên nhân vật cũ</param>
    /// <param name="newName">New character name - Tên nhân vật mới</param>
    /// <returns>True if both updates successful, false otherwise</returns>
    public static bool ChangeCharacterName(string accountId, string oldName, string newName)
    {
        try
        {
            // Validate input parameters
            if (string.IsNullOrEmpty(accountId) || string.IsNullOrEmpty(oldName) || string.IsNullOrEmpty(newName))
            {
                Logger.Instance.Error("ChangeCharacterName: Invalid input parameters");
                return false;
            }

            // Use FreeSql transaction for atomic operations
            _freeSql.Transaction(async () =>
            {
                // Update character name in tbl_xwwl_char
                var charUpdateResult = await _freeSql
                    .Update<tbl_xwwl_char>()
                    .Set(a => a.fld_name == newName)
                    .Where(a => a.fld_id == accountId && a.fld_name == oldName)
                    .ExecuteAffrowsAsync();

                if (charUpdateResult == 0)
                {
                    Logger.Instance.Warning($"No character found with accountId: {accountId} and name: {oldName}");
                    throw new InvalidOperationException($"Character not found: {oldName}");
                }

                // Update character name in tbl_xwwl_warehouse
                var warehouseUpdateResult = await _freeSql
                    .Update<tbl_xwwl_warehouse>()
                    .Set(a => a.fld_name == newName)
                    .Where(a => a.fld_id == accountId && a.fld_name == oldName)
                    .ExecuteAffrowsAsync();

                // Return results for logging
            });

            // Log success
            Logger.Instance.Info($"✓ Character name changed successfully: {oldName} → {newName} (Account: {accountId})");
            return true;
        }
        catch (InvalidOperationException ex)
        {
            // Character not found - this is expected in some cases
            Logger.Instance.Warning($"⚠ Character name change failed: {ex.Message}");
            return false;
        }
        catch (Exception ex)
        {
            // Transaction automatically rolled back by FreeSql on exception
            Logger.Instance.Error($"✗ Error changing character name from '{oldName}' to '{newName}': {ex.Message}");
            return false;
        }
    }

    public static async void UpdateMaritalStatus(string playerName, int status)
    {
        try
        {
            await _freeSql
            .Update<tbl_xwwl_char>()
            .Set(a => a.fld_marital_status == status)
            .Where(a => a.fld_name == playerName)
            .ExecuteAffrowsAsync();
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Error Updating Marital Status " + ex.Message);
        }
    }


    /// <summary>
    /// Create a new pet
    /// </summary>
    /// <param name="petName"></param>
    /// <param name="owner"></param>
    /// <param name="type"></param>
    /// <param name="id"></param>
    /// <returns></returns>
    public static async Task<bool> CreatePet(string petName, string owner, int type, long id)
    {
        try
        {
            var existing = await _freeSql.Select<tbl_xwwl_cw>()
            .Where(a => a.name == petName)
            .FirstAsync();
            if (existing != null)
            {
                Logger.Instance.Error($"Pet name {petName} already exists");
                return false;
            }
            var pet = new tbl_xwwl_cw
            {
                zrname = owner,
                name = petName,
                itmeid = (int)id,
                fld_zcd = 1,
                fld_exp = "0",
                fld_level = 1,
                fld_bs = 0,
                fld_job = type,
                fld_job_level = 1,
                fld_hp = 100,
                fld_mp = 100,
                fld_kongfu = new byte[100],
                fld_wearitem = new byte[5 * World.Item_Db_Byte_Length],
                fld_item = new byte[16 * World.Item_Db_Byte_Length],
                fld_magic1 = 0,
                fld_magic2 = 0,
                fld_magic3 = 0,
                fld_magic4 = 0,
                fld_magic5 = 0,
                fld_sxbl = 0,
            };
            var result = await _freeSql.Insert(pet).ExecuteAffrowsAsync();
            return result > 0;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Error Creating pet " + ex.Message);
            return false;
        }

    }


    public static async Task<int> IsCharacterOnline(string characterName)
    {
        try
        {
            var character = await _freeSql
            .Select<tbl_xwwl_char>()
            .Where(a => a.fld_name == characterName)
            .FirstAsync();
            return character?.fld_online ?? 0;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Error Checking character online " + ex.Message);
            return 0;
        }
    }

    /// <summary>
    /// Record item synthesis/crafting activity
    /// Ghi lại hoạt động tổng hợp/chế tạo vật phẩm
    /// </summary>
    /// <param name="userId">User ID (Account ID)</param>
    /// <param name="userName">Character name</param>
    /// <param name="synthesisType">Type of synthesis (e.g., "Upgrade", "Craft", "Enhance")</param>
    /// <param name="operationId">Operation/Recipe ID</param>
    /// <param name="synthesisMethod">Synthesis method description</param>
    /// <param name="result">Result status ("Success", "Failed")</param>
    /// <param name="item">The resulting item from synthesis</param>
    /// <returns>True if record saved successfully</returns>
    public static async Task<bool> SynthesisRecord(string userId, string userName, string synthesisType, int operationId, string synthesisMethod, string result, Item item)
    {
        try
        {
            // Check if synthesis recording is enabled
            if (World.SyntheticRecord != 1)
            {
                return true; // Return true but don't record if disabled
            }

            if (string.IsNullOrEmpty(userId) || string.IsNullOrEmpty(userName) || item == null)
            {
                Logger.Instance.Error("Invalid parameters for synthesis record");
                return false;
            }

            // Create synthesis record entity
            var record = new syntheticrecord
            {
                fld_id = userId,                           // User/Account ID
                fld_name = userName,                       // Character name
                fld_qjid = item.GetItemGlobal_ID,         // Item global ID (unique instance)
                fld_pid = (int)item.GetVatPham_ID,        // Item template ID
                fld_iname = item.GetItemName(),           // Item name
                fld_magic0 = item.FLD_MAGIC0,             // Item magic properties
                fld_magic1 = item.FLD_MAGIC1,
                fld_magic2 = item.FLD_MAGIC2,
                fld_magic3 = item.FLD_MAGIC3,
                fld_magic4 = item.FLD_MAGIC4,
                fld_type = synthesisType ?? "Unknown",     // Type of synthesis
                fld_czid = operationId,                    // Operation/Recipe ID
                fld_success = result ?? "Unknown",         // Success/Failure status
                fld_qhjd = item.FLD_CuongHoaSoLuong,      // Enhancement level
                created_at = DateTime.Now                  // Timestamp
            };

            // Insert record into database
            var rowsAffected = await _freeSql
                .Insert(record)
                .ExecuteAffrowsAsync();

            if (rowsAffected > 0)
            {
                Logger.Instance.Debug($"✓ Synthesis record saved: {userName} - {synthesisType} - {item.GetItemName()} - {result}");
                return true;
            }
            else
            {
                Logger.Instance.Error($"✗ Failed to save synthesis record for {userName}");
                return false;
            }
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Error saving synthesis record: {ex.Message}");
            return false;
        }
    }



    public static async void UpdateDivorce(string playerName)
    {
        try
        {
            await _freeSql
            .Update<tbl_xwwl_char>()
            .Set(a => a.fld_qlname == string.Empty)
            .Set(a => a.fld_qldu == 0)
            .Set(a => a.fld_love_word == string.Empty)
            .Set(a => a.fld_marital_status == 0)
            .Set(a => a.fld_married == 0)
            .Where(a => a.fld_name == playerName)
            .ExecuteAffrowsAsync();
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Error Updating Divorce " + ex.Message);
        }
    }

    public static async void UpdateFirstTimeGift(string playerName)
    {
        try
        {
            await _freeSql
            .Update<tbl_xwwl_char>()
            .Set(a => a.nhanqualandau == true)
            .Where(a => a.fld_name == playerName)
            .ExecuteAffrowsAsync();
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Error Updating First Time Gift " + ex.Message);
        }
    }

    public async static Task<bool> CheckFirstTimeGift(string playerName)
    {
        try
        {
            var gift = await _freeSql
            .Select<tbl_xwwl_char>()
            .Where(a => a.fld_name == playerName)
            .FirstAsync();
            return gift?.nhanqualandau ?? false;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Error Checking First Time Gift " + ex.Message);
            return false;
        }
    }

    public static Task<int> CheckDailyHonor(string playerName, string date)
    {
        try
        {
            var honor = _freeSql
            .Select<tbl_xwwl_char>()
            .Where(a => a.fld_name == playerName && a.vohuan_time == date)
            .First();
            return Task.FromResult(honor?.vohuan_gioihan_theongay ?? 0);
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Error Checking Daily Honor " + ex.Message);
            return Task.FromResult(0);
        }
    }
    public static void UpdateDailyHonor(string playerName, string date, int honor)
    {
        try
        {
            _freeSql
            .Update<tbl_xwwl_char>()
            .Set(a => a.vohuan_gioihan_theongay == honor)
            .Set(a => a.vohuan_time == date)
            .Where(a => a.fld_name == playerName)
            .ExecuteAffrows();
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Error Updating Daily Honor " + ex.Message);
        }
    }


    /// <summary>
    /// Create Bird Mail
    /// </summary>
    /// <param name="fname"></param>
    /// <param name="sname"></param>
    /// <param name="npcid"></param>
    /// <param name="msg"></param>
    /// <param name="type"></param>
    public static async void CreateBirdMail(string fname, string sname, int npcid, string msg, int type)
    {
        try
        {
            await _freeSql
            .Insert(new tbl_truyenthuhethong
            {
                nguoinhanthu_nhatvatten = fname,
                guithu_npc = npcid,
                nguoiguithu_ten = sname,
                truyenthunoidung = msg,
                truyenthuthoigian = DateTime.Now,
                danhdaudaxem = type
            }).ExecuteAffrowsAsync();
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Error Creating Bird Mail " + ex.Message);
        }

    }

    public static async void UpdateBirdMail(int mailId, int status)
    {
        try
        {
            await _freeSql
            .Update<tbl_truyenthuhethong>()
            .Set(a => a.danhdaudaxem == status)
            .Where(a => a.id == mailId)
            .ExecuteAffrowsAsync();
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Error Updating Bird Mail " + ex.Message);
        }
    }

    /// <summary>
    /// Add Master-Apprentice Relationship
    /// Thêm quan hệ sư đồ
    /// </summary>
    /// <param name="playerName">Tên đệ tử (Apprentice name)</param>
    /// <param name="apprenticeName">Tên sư phụ (Master name)</param>
    /// <returns>True if successful, False if failed</returns>
    public static async Task<bool> AddMasterApprenticeRelation(string playerName, string apprenticeName, int masterLevel)
    {
        try
        {
            if (string.IsNullOrEmpty(playerName) || string.IsNullOrEmpty(apprenticeName))
            {
                Logger.Instance.Error("Player name or master name cannot be null or empty");
                return false;
            }

            // Check if player (apprentice) already has a master-apprentice relationship
            var existingRelation = await _freeSql
                .Select<tbl_sudosolieu>()
                .Where(a => a.fld_tname == playerName)
                .FirstAsync();

            if (existingRelation != null)
            {
                Logger.Instance.Error($"Player {playerName} already has a master-apprentice relationship");
                return false;
            }

            // Check how many apprentices the master already has (max 3)
            var existingApprentices = await _freeSql
                .Select<tbl_sudosolieu>()
                .Where(a => a.fld_sname == apprenticeName)
                .CountAsync();

            if (existingApprentices >= 3)
            {
                Logger.Instance.Error($"Master {apprenticeName} already has maximum apprentices (3)");
                return false;
            }

            // Find the next available apprentice index (0-2)
            var usedIndices = await _freeSql
                .Select<tbl_sudosolieu>()
                .Where(a => a.fld_sname == apprenticeName)
                .ToListAsync(a => a.fld_index ?? 0);

            var availableIndex = 0;
            for (int i = 0; i < 3; i++)
            {
                if (!usedIndices.Contains(i))
                {
                    availableIndex = i;
                    break;
                }
            }

            // Get master level from character data (default to 1 if not found)
            try
            {
                var masterChar = await _freeSql
                    .Select<tbl_xwwl_char>()
                    .Where(a => a.fld_name == apprenticeName)
                    .FirstAsync();

                if (masterChar != null)
                {
                    masterLevel = masterChar.fld_level ?? 1;
                }
            }
            catch
            {
                // Use default level if character not found
                masterLevel = 1;
            }

            // Create new master-apprentice relationship
            var newRelation = new tbl_sudosolieu
            {
                fld_tname = playerName,          // Apprentice name (đệ tử)
                fld_index = availableIndex,      // Apprentice index (0-2)
                fld_sname = apprenticeName,      // Master name (sư phụ)
                fld_tlevel = masterLevel,        // Master level when relationship created
                fld_stlevel = 1,                 // Starting apprentice level
                fld_styhd = 0,                   // Apprentice training progress
                fld_stwg1 = 0,                   // Martial arts skill 1
                fld_stwg2 = 0,                   // Martial arts skill 2
                fld_stwg3 = 0                    // Martial arts skill 3
            };

            // Insert the relationship into database
            var result = await _freeSql
                .Insert(newRelation)
                .ExecuteAffrowsAsync();

            if (result > 0)
            {
                Logger.Instance.Info($"✓ Master-Apprentice relationship created: {apprenticeName} -> {playerName} (Index: {availableIndex})");
                return true;
            }
            else
            {
                Logger.Instance.Error($"✗ Failed to insert master-apprentice relationship into database");
                return false;
            }
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Error adding master-apprentice relationship: {ex.Message}");
            return false;
        }
    }


    public static async Task<bool> RemoveMasterApprenticeRelation(string playerName, string apprenticeName)
    {
        try
        {
            return await _freeSql
            .Delete<tbl_sudosolieu>()
            .Where(a => a.fld_tname == playerName && a.fld_sname == apprenticeName)
            .ExecuteAffrowsAsync() > 0;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Error Removing Master Apprentice Relation " + ex.Message);
            return false;
        }
    }

    public static async Task<bool> CheckCharacterExists(string characterName)
    {
        try
        {
            if (!await IsCharacterNameAvailable(characterName))
            {
                Logger.Instance.Error($"Character name {characterName} is not available (already exists or similar name found)");
                return true;
            }
            var character = await _freeSql
            .Select<tbl_xwwl_char>()
            .Where(a => a.fld_name == characterName)
            .CountAsync();
            return character > 0;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Error Checking character exists " + ex.Message);
            return false;
        }
    }


    /// <summary>
    /// Kiểm tra tên nhân vật có hợp lệ không (không trùng lặp, không phân biệt chữ hoa thường)
    /// Check if character name is valid (not duplicate, case-insensitive)
    /// </summary>
    /// <param name="characterName">Tên nhân vật cần kiểm tra</param>
    /// <returns>True nếu tên hợp lệ, False nếu đã tồn tại</returns>
    public static async Task<bool> IsCharacterNameAvailable(string characterName)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(characterName))
                return false;

            var existingChar = await _freeSql.Select<tbl_xwwl_char>()
                .Where(a => a.fld_name.ToLower() == characterName.ToLower())
                .FirstAsync();

            return existingChar == null;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Error checking character name availability: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Tạo nhân vật mới
    /// </summary>
    /// <param name="accountId"></param>
    /// <param name="characterName"></param>
    /// <param name="job"></param>
    /// <param name="option"></param>
    /// <returns></returns>
    public static async Task<bool> CreateCharacter(string accountId, string characterName, int job, byte[] option)
    {
        try
        {
            // Check if character name is available (case-insensitive to prevent similar names)
            // Kiểm tra tên nhân vật có khả dụng không (không phân biệt chữ hoa thường để tránh tên giống nhau)
            if (!await IsCharacterNameAvailable(characterName))
            {
                Logger.Instance.Error($"Character name {characterName} is not available (already exists or similar name found)");
                return false;
            }

            // Check if account already has 4 characters (max limit)
            var existingChars = await _freeSql.Select<tbl_xwwl_char>()
                .Where(a => a.fld_id == accountId)
                .ToListAsync();

            if (existingChars.Count >= 4)
            {
                Logger.Instance.Error($"Account {accountId} already has maximum characters (4)");
                return false;
            }

            // Find the first available character index (0-3)
            var characterIndex = 0;
            var usedIndices = existingChars.Select(c => c.fld_index ?? 0).ToList();

            for (var i = 0; i < 4; i++)
            {
                if (!usedIndices.Contains(i))
                {
                    characterIndex = i;
                    break;
                }
            }

            // Create item arrays based on SetUserName logic
            var wearItemArray = new byte[1095];
            var inventoryArray = new byte[2736];
            // Get starting weapon based on job
            var weaponId = job switch
            {
                1 => *********,
                2 => *********,
                3 => *********,
                4 => *********,
                5 => *********,
                6 => *********,
                7 => *********,
                8 => 100204001,
                9 => 200204001,
                10 => 900200001,
                11 => 400204001,
                12 => 300204001,
                13 => 500204001,
                _ => *********,
            };

            var item = World.CreateAnItem(weaponId, 1);

            Buffer.BlockCopy(item.VatPham_byte, 0, inventoryArray, 0, World.Item_Db_Byte_Length);
            var firstTimeGift = World.CreateAnItem(World.FirstLoginGift, 1);
            Buffer.BlockCopy(firstTimeGift.VatPham_byte, 0, inventoryArray, World.Item_Db_Byte_Length, World.Item_Db_Byte_Length);
            if (job == 4 || job == 11)
            {
                var arrow = World.CreateAnItem(1000000148, 100);
                Buffer.BlockCopy(arrow.VatPham_byte, 0, inventoryArray, World.Item_Db_Byte_Length * 2, World.Item_Db_Byte_Length);
            }

            // Set HP and MP based on job
                var (hp, mp) = job switch
            {
                4 => (124, 116),
                6 => (130, 114),
                7 => (124, 136),
                1 or 8 => (145, 116),
                10 => (145, 116),
                11 => (124, 116),
                2 or 3 or 5 or 9 or 12 => (133, 118),
                13 => (118, 136),
                _ => (145, 116)
            };

            // Create character entity
            var character = new tbl_xwwl_char
            {
                // Basic character info
                fld_id = accountId,
                fld_name = characterName,
                fld_index = characterIndex,
                fld_job = job,
                fld_level = 1,
                fld_exp = "0",
                fld_zx = 0,
                fld_job_level = 0,

                // Position and map
                fld_x = 418.0,
                fld_y = 1780.0,
                fld_z = 15.0,
                fld_menow = 101,

                // Character stats
                fld_hp = hp,
                fld_mp = mp,
                fld_sp = 0,
                fld_wx = 0,
                fld_se = 0,
                fld_point = 0,

                // Character appearance and equipment
                fld_face = option, // Character appearance data
                fld_wearitem = wearItemArray, // Empty equipped items
                fld_item = inventoryArray, // Starting inventory with weapon
                fld_qitem = new byte[300], // Quest items
                fld_ntcitem = new byte[600], // Notice items
                fld_coatitem = new byte[8000], // Coat items
                fld_fashion_item = new byte[5012], // Fashion items
                fld_pinkbag_item = new byte[2304], // Pink bag items
                fld_nszitem = new byte[600], // NSZ items

                // Skills and abilities
                fld_skills = new byte[100], // Character skills
                fld_kongfu = new byte[1404], // Kung fu data
                fld_hits = new byte[250], // Hit data
                fld_ljkongfu = new byte[128], // LJ kung fu
                fld_thangthienkhicong = new byte[256], // Thang thien khi cong
                fld_thangthienvocong = new byte[256], // Thang thien vo cong
                fld_thangthienlichluyen = 0,
                fld_thangthienvocongdiemso = 0,
                fld_thannuvocongdiemso = 0,
                fld_asc7_anti_qigong = new byte[128],

                // Quest and door data
                fld_doors = new byte[1200], // Door data
                fld_quest = new byte[1200], // Quest data
                fld_quest_finish = new byte[1000], // Finished quests
                fld_dayquest = "",

                // Money and resources
                fld_money = "10000",
                fld_jl = "0", // JL points

                // Combat and PvP
                fld_lumpid = 0,
                fld_fight_exp = 0,
                fld_j9 = 0,
                fld_jq = 0,
                fld_pvp_piont = 0,
                solangietnguoi = 0,
                bigietsolan = 0,

                // Guild and social
                fld_qlname = "", // Guild name
                fld_qljzname = "", // Guild leader name
                fld_qldu = 0,
                fld_qldumax = 0,
                fld_qlrank = 0,
                bangphai_doconghien = 0,

                // Additional stats
                fld_add_hp = 0,
                fld_add_at = 0,
                fld_add_df = 0,
                fld_add_hb = 0,
                fld_add_mp = 0,
                fld_add_mz = 0,
                fld_add_clvc = 0,
                fld_add_ptvc = 0,
                fld_add_kc = 0,

                // System flags
                fld_zbver = 1, // Version flag
                fld_zztype = 0,
                fld_zzsl = 0,
                fld_zs = 0,
                fld_online = 0,
                fld_get_wx = 0,
                fld_tongkim = 0,
                fld_taisinh = 0,
                fld_vipdj = 0,
                fld_七彩 = 0,
                fld_vip_at = 0,
                fld_vip_df = 0,
                fld_vip_hp = 0,
                fld_vip_level = 0,
                fld_zscs = 0,
                fld_sjjl = 0,
                fld_在线时间 = 0.0,
                fld_在线等级 = 0,
                fld_领奖标志 = 0,
                fld_reserved = 0,
                fld_签名类型 = 0,
                fld_任务等级4 = 0,

                // Time data
                fld_ctime = new byte[480],
                fld_ctimenew = new byte[240],
                fld_stime = new byte[320],
                fld_chtime = new byte[320],
                在线时间 = new byte[200],

                // Master-student system
                fld_师傅 = "",
                fld_徒弟1 = "",
                fld_徒弟2 = "",
                fld_徒弟3 = "",
                fld_师徒武功1_1 = 0,
                fld_师徒武功1_2 = 0,
                fld_师徒武功1_3 = 0,
                fld_suphu = new byte[28],
                fld_detu = new byte[60],
                fld_sudovocong = new byte[200],

                // Name and appearance
                fld_nametype = Converter.hexStringToByte2("000000000000000000000000000000000100000000000000000000000000000002000000000000000000000000000000"),
                fld_kieutoc = new byte[4], // Hair style
                fld_khuonmat = new byte[4], // Face

                // Special systems
                fld_tlc = 0,
                fld_fqid = "d1",
                fld_giaitruthoigian = "",
                fld_titlepoints = 0,
                fld_rosetitlepoints = 0,
                fld_speakingtype = 0,
                fld_mlz = 0,

                // Marriage system
                fld_love_word = "",
                fld_marital_status = 0,
                fld_married = 0,
                fld_jh_date = DateTime.Now,

                // Time limits
                fld_fb_time = 0,
                fld_lost_wx = 0,
                fld_hd_time = 0,
                fld_whtb = 0,

                // Configuration
                fld_config = "",

                // Version and flags
                version = 1,
                nhanqualandau = false,
                tlc_random_phe = "",
                vohuan_gioihan_theongay = 0,
                vohuan_time = "",
                fld_moneyextralevel = 0,

                // Special time
                congthanhchienthoigian = null,
                fld_xb = 0,
            };

            // Insert character into database
            Logger.Instance.Info($"Inserting character {characterName} for account {accountId} (index: {characterIndex})");
            var result = await _freeSql.Insert(character).ExecuteAffrowsAsync();

            if (result > 0)
            {
                Logger.Instance.Info($"✓ Character {characterName} created successfully for account {accountId}");
                return true;
            }
            else
            {
                Logger.Instance.Error($"✗ Failed to insert character {characterName} into database");
                return false;
            }
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Error creating character {characterName}: {ex.Message}");
            return false;
        }
    }


    /// <summary>
    /// Set Character Online
    /// </summary>
    /// <param name="userId"></param>
    /// <param name="characterName"></param>
    /// <param name="status"></param>

    public static async void SetCharacterOnline(string userId, string characterName, int status)
    {
        try
        {
            await _freeSql
            .Update<tbl_xwwl_char>()
            .Set(a => a.fld_online == status)
            .Where(a => a.fld_id == userId && a.fld_name == characterName)
            .ExecuteAffrowsAsync();
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Error Setting character online " + ex.Message);
        }
    }
    /// <summary>
    /// Get And Create Public Warehouse
    /// </summary>
    /// <param name="userId"></param>
    /// <returns></returns>

    public static async Task<tbl_xwwl_publicwarehouse> GetAndCreatePublicWarehouse(string userId)
    {
        try
        {
            var warehouse = await _freeSql
            .Select<tbl_xwwl_publicwarehouse>()
            .Where(a => a.fld_id == userId)
            .FirstAsync();
            if (warehouse == null)
            {
                warehouse = new tbl_xwwl_publicwarehouse
                {
                    fld_id = userId,
                    fld_money = "0",
                    fld_item = new byte[World.Item_Db_Byte_Length * 60],
                    fld_itime = new byte[50],
                    fld_zbver = 1,
                };
                await _freeSql.Insert(warehouse).ExecuteAffrowsAsync();
            }
            return warehouse;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Error Getting and Creating Public Warehouse " + ex.Message);
            return null;
        }
    }
    /// <summary>
    /// Get And Create Warehouse
    /// </summary>
    /// <param name="userId"></param>
    /// <param name="characterName"></param>
    /// <returns></returns>

    public static async Task<tbl_xwwl_warehouse> GetAndCreateWarehouse(string userId, string characterName)
    {
        try
        {
            var warehouse = await _freeSql
            .Select<tbl_xwwl_warehouse>()
            .Where(a => a.fld_id == userId && a.fld_name == characterName)
            .FirstAsync();
            if (warehouse == null)
            {
                warehouse = new tbl_xwwl_warehouse
                {
                    fld_id = userId,
                    fld_name = characterName,
                    fld_money = "0",
                    fld_item = new byte[World.Item_Db_Byte_Length * 60],
                };
                await _freeSql.Insert(warehouse).ExecuteAffrowsAsync();
            }
            return warehouse;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Error Getting and Creating Warehouse " + ex.Message);
            return null;
        }
    }



    public static async void UpdateGoldExtra(string characterName, int goldExtra)
    {
        try
        {
            await _freeSql
            .Update<tbl_xwwl_char>()
            .Set(a => a.fld_moneyextralevel == goldExtra)
            .Where(a => a.fld_name == characterName)
            .ExecuteAffrowsAsync();
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Error Updating gold extra " + ex.Message);
        }
    }

    public static async Task<List<tbl_truyenthuhethong>> LoadBirdMailList(string characterName)
    {
        try
        {
            return await _freeSql
            .Select<tbl_truyenthuhethong>()
            .Where(a => a.nguoinhanthu_nhatvatten == characterName)
            .ToListAsync();
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Error Loading bird mail list " + ex.Message);
            return null;
        }
    }

    public static async Task<List<tbl_sudosolieu>> LoadStudentList(string characterName)
    {
        try
        {
            return await _freeSql
            .Select<tbl_sudosolieu>()
            .Where(a => a.fld_sname == characterName)
            .ToListAsync();
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Error Loading student list " + ex.Message);
            return null;
        }
    }

    public static async Task<tbl_sudosolieu> LoadTeacherList(string characterName)
    {
        try
        {
            return await _freeSql
            .Select<tbl_sudosolieu>()
            .Where(a => a.fld_tname == characterName)
            .FirstAsync();
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Error Loading teacher list " + ex.Message);
            return null;
        }
    }

    #endregion

    #region ThienMaThanCung

    public static async Task<bool> DeleteSiegeParticipants()
    {
        try
        {
            var result = await _freeSql
            .Delete<congthanhchien_thanhchu>()
            .ExecuteAffrowsAsync();
            return result > 0;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Error Deleting siege participants " + ex.Message);
            return false;
        }
    }

    public static async Task<List<congthanhchien_thanhchu>> FindAllSiegeParticipants()
    {
        try
        {
            return await _freeSql
            .Select<congthanhchien_thanhchu>()
            .ToListAsync();
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Error Finding all siege participants " + ex.Message);
            return null;
        }
    }

    public static async void InsertSiege(string guildName, string guildmaster, int guildId)
    {
        try
        {
            await _freeSql
            .Insert(new congthanhchien_thanhchu
            {
                tenthanhchu = guildmaster,
                congthanhchien_tenbang = guildName,
                bangphaiid = guildId,
                congthanhthoigian = DateTime.Now,
                congthanhbanthuongthoigian = DateTime.Now
            })
            .ExecuteAffrowsAsync();
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Error Inserting siege " + ex.Message);
        }
    }

    public static async Task<thienmathancung_danhsach> LoadThienMaThanCung()
    {
        try
        {
            return await _freeSql
            .Select<thienmathancung_danhsach>()
            .FirstAsync();
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Error Loading thien ma than cung " + ex.Message);
            return null;
        }
    }

    public static async Task<int> UpdateGuildAllianceMaster(string guildName, string newMasterName, int status)
    {
        try
        {
            return await _freeSql
            .Update<tbl_xwwl_guild>()
            .Set(a => a.lienminh_minhchu == newMasterName)
            .Set(a => a.thongbao_congthanh == status)
            .Where(a => a.g_name == guildName)
            .ExecuteAffrowsAsync();
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Error Updating guild alliance master " + ex.Message);
            return -1;
        }
    }

    // public static async Task<int> UpdateThienMaThanCung(string bangChiemThanh, string ngayChiemThanh, int congThanhCuongHoaLevel)
    // {
    //     try
    //     {
    //         return await _freeSql
    //         .Update<thienmathancung_danhsach>()
    //         .Set(a => a.bang_chiem_thanh == bangChiemThanh)
    //         .Set(a => a.ngay_chiem_thanh == ngayChiemThanh)
    //         .Set(a => a.cong_thanh_cuonghoa_level == congThanhCuongHoaLevel)
    //         .Set(a => a.thoigian_lammoi_congthanh == DateTime.Now)
    //         .ExecuteAffrowsAsync();
    //     }
    //     catch (Exception ex)
    //     {
    //         Logger.Instance.Error($"Error Updating thien ma than cung " + ex.Message);
    //         return -1;
    //     }
    // }
    #endregion

    #region Log

    public static async void DrugRecord(string userId, string userName, int itemId, int itemCount)
    {
        try
        {
            await _freeSql.Insert(new drugrecord
            {
                accountid = userId,
                charactername = userName,
                itemid = itemId,
                amount = itemCount
            }).ExecuteAffrowsAsync();
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Error Recording drug " + ex.Message);
        }
    }

    public static void DropRecord(string userId, string userName, int itemId, int itemCount, int mapId, int x, int y)
    {
        // try
        // {
        //     await _freeSql.Insert(new droprecord
        //     {
        //         accountid = userId,
        //         charactername = userName,
        //         itemid = itemId,
        //         amount = itemCount,
        //         mapid = mapId,
        //         x = x,
        //         y = y
        //     }).ExecuteAffrowsAsync();
        // }
        // catch (Exception ex)
        // {
        //     Logger.Instance.Error($"Error Recording drop " + ex.Message);
        // }
    }

    public static void StoreRecord(string userId, string userName, int itemId, int itemCount, int type, long price)
    {
        // try
        // {
        //     await _freeSql.Insert(new storerecord
        //     {
        //         accountid = userId,
        //         charactername = userName,
        //         itemid = itemId,
        //         amount = itemCount,
        //         type = type,
        //         price = price
        //     }).ExecuteAffrowsAsync();
        // }
        // catch (Exception ex)
        // {
        //     Logger.Instance.Error($"Error Recording store " + ex.Message);
        // }
    }



    /// <summary>
    /// Record item transaction/transfer activity
    /// Ghi lại hoạt động giao dịch/chuyển vật phẩm
    /// </summary>
    /// <param name="userId">Source user ID - ID người gửi</param>
    /// <param name="userName">Source user name - Tên người gửi</param>
    /// <param name="toUserId">Target user ID - ID người nhận</param>
    /// <param name="toUserName">Target user name - Tên người nhận</param>
    /// <param name="globalId">Item global ID - ID toàn cục của vật phẩm</param>
    /// <param name="itemId">Item template ID - ID template vật phẩm</param>
    /// <param name="itemName">Item name - Tên vật phẩm</param>
    /// <param name="itemCount">Item quantity - Số lượng vật phẩm</param>
    /// <param name="itemProperties">Item properties - Thuộc tính vật phẩm</param>
    /// <param name="money">Money amount involved - Số tiền liên quan</param>
    /// <param name="type">Transaction type - Loại giao dịch</param>
    public static async Task ItemRecord(string userId, string userName, string toUserId, string toUserName, double globalId, int itemId, string itemName, int itemCount, string itemProperties, int money, string type)
    {
        try
        {
            // Check if item recording is enabled
            if (World.ItemRecord != 1)
            {
                return; // Don't record if disabled
            }

            // Validate required parameters
            if (string.IsNullOrEmpty(userId) || string.IsNullOrEmpty(userName))
            {
                Logger.Instance.Error("ItemRecord: Source user ID or name cannot be null or empty");
                return;
            }

            // Create item record entity
            var record = new itemrecord
            {
                userid = userId,                                    // Source user ID
                username = userName,                                // Source user name
                touserid = toUserId ?? string.Empty,                // Target user ID (can be empty for some transactions)
                tousername = toUserName ?? string.Empty,            // Target user name (can be empty for some transactions)
                global_id = globalId.ToString(),                    // Item global ID as string
                vatpham_id = itemId.ToString(),                     // Item template ID as string
                vatphamten = itemName ?? string.Empty,              // Item name
                vatphamsoluong = itemCount,                         // Item quantity (correct property name)
                vatphamthuoctinh = itemProperties ?? string.Empty,  // Item properties (correct property name)
                sotien = money,                                     // Money amount
                loaihinh = type ?? "Unknown",                       // Transaction type
                thoigian = DateTime.Now                             // Transaction timestamp
            };

            // Insert record into database
            var rowsAffected = await _freeSql
                .Insert(record)
                .ExecuteAffrowsAsync();

            if (rowsAffected > 0)
            {
                Logger.Instance.Debug($"✓ Item record saved: {userName} -> {toUserName ?? "System"} | {itemName} x{itemCount} | Type: {type}");
            }
            else
            {
                Logger.Instance.Error($"✗ Failed to save item record for {userName}");
            }
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Error recording item transaction: {ex.Message}");
        }
    }



    #endregion

    #region Account & Login Management

    /// <summary>
    /// Set user status to offline
    /// Đặt trạng thái người dùng thành offline
    /// </summary>
    /// <param name="accountId">Account ID - ID tài khoản</param>
    public static void SetUserOffline(string accountId)
    {
        try
        {
            if (string.IsNullOrEmpty(accountId))
            {
                Logger.Instance.Error("SetUserOffline: Account ID cannot be null or empty");
                return;
            }

            // Update account status to offline using AccountDb
            AccountDb.UpdateRemoveUserLogin(accountId);
            Logger.Instance.Debug($"✓ User {accountId} set to offline successfully");
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Error setting user offline: {ex.Message}");
        }
    }

    /// <summary>
    /// Delete MAC address record when user logs out
    /// Xóa địa chỉ MAC khi người dùng đăng xuất
    /// </summary>
    /// <param name="username">Username - Tên người dùng</param>
    public static async void DeleteRecoveryMacAddressLogout(string username)
    {
        try
        {
            if (string.IsNullOrEmpty(username))
            {
                Logger.Instance.Error("DeleteRecoveryMacAddressLogout: Username cannot be null or empty");
                return;
            }

            // Delete MAC address records for login type
            var rowsAffected = await _freeSql
                .Delete<loginrecord_mac>()
                .Where(a => a.username == username && a.loaihinh == "Login")
                .ExecuteAffrowsAsync();

            if (rowsAffected > 0)
            {
                Logger.Instance.Debug($"✓ Deleted {rowsAffected} MAC address records for user: {username}");
            }
            else
            {
                Logger.Instance.Debug($"ℹ No MAC address records found for user: {username}");
            }
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Error deleting MAC address records: {ex.Message}");
        }
    }

    /// <summary>
    /// Record user login information
    /// Ghi lại thông tin đăng nhập của người dùng
    /// </summary>
    /// <param name="userId">User ID - ID người dùng</param>
    /// <param name="userName">Username - Tên người dùng</param>
    /// <param name="userIp">IP address - Địa chỉ IP</param>
    /// <param name="loaiHinh">Login type - Loại hình đăng nhập</param>
    /// <param name="macAddress">MAC address - Địa chỉ MAC</param>
    public static async void LoginRecord(string userId, string userName, string userIp, string loaiHinh, string macAddress)
    {
        try
        {
            // Check if login recording is enabled
            if (World.LoginRecord != 1)
            {
                return;
            }

            if (string.IsNullOrEmpty(userId) || string.IsNullOrEmpty(userName))
            {
                Logger.Instance.Error("LoginRecord: User ID or username cannot be null or empty");
                return;
            }

            // Create login record
            var record = new loginrecord
            {
                userid = userId,                        // User ID
                username = userName,                    // Username
                userip = userIp ?? string.Empty,        // IP address
                loaihinh = loaiHinh ?? "Login",         // Login type
                mac_address = macAddress ?? string.Empty, // MAC address
                thoigian = DateTime.Now                 // Login timestamp
            };

            // Insert record into database
            var rowsAffected = await _freeSql
                .Insert(record)
                .ExecuteAffrowsAsync();

            if (rowsAffected > 0)
            {
                Logger.Instance.Debug($"✓ Login record saved: {userName} from {userIp}");
            }
            else
            {
                Logger.Instance.Error($"✗ Failed to save login record for {userName}");
            }
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Error recording login: {ex.Message}");
        }
    }

    /// <summary>
    /// Record user login information with MAC address and server ID
    /// Ghi lại thông tin đăng nhập của người dùng kèm địa chỉ MAC và ID máy chủ
    /// </summary>
    /// <param name="userId">User ID - ID người dùng</param>
    /// <param name="userName">Username - Tên người dùng</param>
    /// <param name="userIp">IP address - Địa chỉ IP</param>
    /// <param name="loaiHinh">Login type - Loại hình đăng nhập</param>
    /// <param name="macAddress">MAC address - Địa chỉ MAC</param>
    /// <param name="serverId">Server ID - ID máy chủ</param>
    public static async void LoginRecordMac(string userId, string userName, string userIp, string loaiHinh, string macAddress, int serverId)
    {
        try
        {
            // Check if login recording is enabled
            if (World.LoginRecord != 1)
            {
                return;
            }

            if (string.IsNullOrEmpty(userId) || string.IsNullOrEmpty(userName))
            {
                Logger.Instance.Error("LoginRecordMac: User ID or username cannot be null or empty");
                return;
            }

            // Create login record with MAC and server ID
            var record = new loginrecord_mac
            {
                userid = userId,                        // User ID
                username = userName,                    // Username
                userip = userIp ?? string.Empty,        // IP address
                loaihinh = loaiHinh ?? "Login",         // Login type
                mac_address = macAddress ?? string.Empty, // MAC address
                serverid = serverId,                    // Server ID
                thoigian = DateTime.Now                 // Login timestamp
            };

            // Insert record into database
            var rowsAffected = await _freeSql
                .Insert(record)
                .ExecuteAffrowsAsync();

            if (rowsAffected > 0)
            {
                Logger.Instance.Debug($"✓ Login MAC record saved: {userName} from {userIp} on server {serverId}");
            }
            else
            {
                Logger.Instance.Error($"✗ Failed to save login MAC record for {userName}");
            }
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Error recording login with MAC: {ex.Message}");
        }
    }

    #endregion

    #region Guild Honor

    /// <summary>
    /// Set guild honor points
    /// Cập nhật điểm vinh dự cho bang phái
    /// </summary>
    /// <param name="guildName">Guild name - Tên bang phái</param>
    /// <param name="guildMaster">Guild master name - Tên bang chủ</param>
    /// <param name="faction">Faction - Thế lực</param>
    /// <param name="level">Level - Đẳng cấp</param>
    /// <param name="job">Job - Nghề nghiệp</param>
    /// <param name="points">Honor points to add - Điểm vinh dự cần thêm</param>
    /// <returns>1 if successful, -1 if failed</returns>
    public static async Task<int> SetGuildHonorPoints(string guildName, string guildMaster, int faction, int level, int job, int points)
    {
        try
        {
            if (string.IsNullOrEmpty(guildName))
            {
                Logger.Instance.Error("SetGuildHonorPoints: Guild name cannot be null or empty");
                return -1;
            }

            // Check if guild already exists in honor system (TYPE = 3 for guild)
            var existingRecord = await _freeSql
                .Select<tbl_vinhduhethong>()
                .Where(a => a.fld_type == 3 && a.fld_bangphai == guildName)
                .FirstAsync();

            if (existingRecord == null)
            {
                // Create new guild honor record
                var newRecord = new tbl_vinhduhethong
                {
                    fld_type = 3,                           // Guild honor type
                    fld_nghenghiep = job,                   // Job
                    fld_tennhanvat = string.Empty,          // Empty for guild records
                    fld_dangcap = level,                    // Level
                    fld_theluc = faction,                   // Faction
                    fld_bangphai = guildName,               // Guild name
                    fld_bangphai_bangchu = guildMaster,     // Guild master name
                    fld_diemso = points                     // Honor points
                };

                var result = await _freeSql
                    .Insert(newRecord)
                    .ExecuteAffrowsAsync();

                if (result > 0)
                {
                    Logger.Instance.Info($"✓ Guild honor record created: {guildName} with {points} points");
                    return 1;
                }
                else
                {
                    Logger.Instance.Error($"✗ Failed to create guild honor record for {guildName}");
                    return -1;
                }
            }
            else
            {
                // Update existing guild honor points
                var result = await _freeSql
                    .Update<tbl_vinhduhethong>()
                    .Set(a => a.fld_diemso == a.fld_diemso + points)
                    .Where(a => a.fld_bangphai == guildName && a.fld_type == 3)
                    .ExecuteAffrowsAsync();

                if (result > 0)
                {
                    Logger.Instance.Info($"✓ Guild honor points updated: {guildName} +{points} points");
                    return 1;
                }
                else
                {
                    Logger.Instance.Error($"✗ Failed to update guild honor points for {guildName}");
                    return -1;
                }
            }
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Error setting guild honor points: {ex.Message}");
            return -1;
        }
    }

    /// <summary>
    /// Set player honor points
    /// Cập nhật điểm vinh dự cho người chơi
    /// </summary>
    /// <param name="honorType">Honor type - Loại vinh dự (1=TheLucChien, 2=VoLamHuyetChien, etc.)</param>
    /// <param name="characterName">Character name - Tên nhân vật</param>
    /// <param name="level">Character level - Đẳng cấp</param>
    /// <param name="job">Character job - Nghề nghiệp</param>
    /// <param name="faction">Character faction - Thế lực</param>
    /// <param name="guildName">Guild name - Tên bang phái</param>
    /// <param name="guildMaster">Guild master name - Tên bang chủ</param>
    /// <param name="points">Honor points to add - Điểm vinh dự cần thêm</param>
    /// <returns>1 if successful, -1 if failed</returns>
    public static async void SetPlayerHonorPoints(int honorType, string characterName, int level, int job, int faction, string guildName, string guildMaster, int points)
    {
        try
        {
            if (string.IsNullOrEmpty(characterName))
            {
                Logger.Instance.Error("SetPlayerHonorPoints: Character name cannot be null or empty");
                return;
            }

            // Get guild master name if guild is provided
            if (!string.IsNullOrEmpty(guildName))
            {
                var guild = await FindGuild(guildName);
                if (guild != null)
                {
                    guildMaster = guild.g_master;
                }
            }

            // Check if player already exists in honor system for this type
            var existingRecord = await _freeSql
                .Select<tbl_vinhduhethong>()
                .Where(a => a.fld_tennhanvat == characterName && a.fld_type == honorType)
                .FirstAsync();

            if (existingRecord == null)
            {
                // Create new player honor record
                var newRecord = new tbl_vinhduhethong
                {
                    fld_type = honorType,                   // Honor type
                    fld_nghenghiep = job,                   // Job
                    fld_tennhanvat = characterName,         // Character name
                    fld_dangcap = level,                    // Level
                    fld_theluc = faction,                   // Faction
                    fld_bangphai = guildName ?? string.Empty, // Guild name
                    fld_bangphai_bangchu = guildMaster ?? string.Empty, // Guild master name
                    fld_diemso = points                     // Honor points
                };

                var result = await _freeSql
                    .Insert(newRecord)
                    .ExecuteAffrowsAsync();

                if (result > 0)
                {
                    Logger.Instance.Info($"✓ Player honor record created: {characterName} (Type: {honorType}) with {points} points");
                }
                else
                {
                    Logger.Instance.Error($"✗ Failed to create player honor record for {characterName}");
                }
            }
            else
            {
                // Update existing player honor points
                var result = await _freeSql
                    .Update<tbl_vinhduhethong>()
                    .Set(a => a.fld_diemso == a.fld_diemso + points)
                    .Where(a => a.fld_tennhanvat == characterName && a.fld_type == honorType)
                    .ExecuteAffrowsAsync();

                if (result > 0)
                {
                    Logger.Instance.Info($"✓ Player honor points updated: {characterName} (Type: {honorType}) +{points} points");
                }
                else
                {
                    Logger.Instance.Error($"✗ Failed to update player honor points for {characterName}");
                }
            }
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Error setting player honor points: {ex.Message}");
        }
    }

    #endregion

    #region Guild Point Management

    /// <summary>
    /// Guild point summary data model
    /// Model dữ liệu tổng hợp điểm bang phái
    /// </summary>
    public class GuildPointSummary
    {
        public string GuildName { get; set; } = string.Empty;
        public int TotalPoints { get; set; }
        public int NewLevel { get; set; }
        public int MemberCount { get; set; }
    }

    /// <summary>
    /// Calculate guild level based on total points
    /// Tính toán cấp độ bang phái dựa trên tổng điểm
    /// </summary>
    /// <param name="totalPoints">Total guild points - Tổng điểm bang phái</param>
    /// <returns>Guild level (1-7)</returns>
    private static int CalculateGuildLevel(int totalPoints)
    {
        return totalPoints switch
        {
            >= 160000 => 7,
            >= 80000 => 7,
            >= 40000 => 7,
            >= 20000 => 7,
            >= 10000 => 7,
            >= 4000 => 7,
            >= 3000 => 6,
            >= 1500 => 5,
            >= 1000 => 4,
            >= 500 => 3,
            >= 200 => 2,
            _ => 1
        };
    }

    /// <summary>
    /// Update all guild points and levels efficiently using batch operations
    /// Cập nhật điểm và cấp độ tất cả bang phái hiệu quả bằng batch operations
    /// </summary>
    /// <returns>List of updated guilds with their new levels</returns>
    public static async Task<List<GuildPointSummary>> UpdateAllGuildPointsAndLevels()
    {
        try
        {
            var updatedGuilds = new List<GuildPointSummary>();

            // Get all guild members first, then group in memory for better compatibility
            var allMembers = await _freeSql
                .Select<tbl_xwwl_guildmember>()
                .Where(a => !string.IsNullOrEmpty(a.g_name))
                .ToListAsync();

            // Group by guild name and calculate totals
            var guildPointSummaries = allMembers
                .GroupBy(a => a.g_name)
                .Select(g => new GuildPointSummary
                {
                    GuildName = g.Key,
                    TotalPoints = g.Sum(x => x.fld_guildpoint ?? 0),
                    MemberCount = g.Count()
                })
                .ToList();

            if (!guildPointSummaries.Any())
            {
                Logger.Instance.Info("No guilds found to update");
                return updatedGuilds;
            }

            // Calculate new levels for each guild
            foreach (var summary in guildPointSummaries)
            {
                summary.NewLevel = CalculateGuildLevel(summary.TotalPoints);
                updatedGuilds.Add(summary);
            }

            // Batch update guild levels and honor points
            var updateTasks = new List<Task>();

            foreach (var guild in updatedGuilds)
            {
                // Update guild level and honor points
                var updateGuildTask = _freeSql
                    .Update<tbl_xwwl_guild>()
                    .Set(a => a.bangphaivohuan == guild.TotalPoints)
                    .Set(a => a.leve == guild.NewLevel)
                    .Where(a => a.g_name == guild.GuildName)
                    .ExecuteAffrowsAsync();

                updateTasks.Add(updateGuildTask);
            }

            // Reset all new guild points to 0 in a single batch operation
            var resetPointsTask = _freeSql
                .Update<tbl_xwwl_guildmember>()
                .Set(a => a.fld_newguildpoint == 0)
                .ExecuteAffrowsAsync();

            updateTasks.Add(resetPointsTask);

            // Execute all updates concurrently
            await Task.WhenAll(updateTasks);

            Logger.Instance.Info($"✓ Updated {updatedGuilds.Count} guilds with new points and levels");
            return updatedGuilds;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Error updating guild points and levels: {ex.Message}");
            return new List<GuildPointSummary>();
        }
    }

    /// <summary>
    /// Optimized version of UpdateGuildPoint function
    /// Phiên bản tối ưu của hàm UpdateGuildPoint
    /// </summary>
    /// <returns>True if any guilds were updated</returns>
    public static async Task<bool> UpdateGuildPointOptimized()
    {
        try
        {
            // Check if there are any new guild points to process
            var hasNewPoints = await CheckGuildHasNewPoint();
            if (!hasNewPoints)
            {
                Logger.Instance.Debug("No new guild points to process");
                return false;
            }

            // Update all guild points and levels efficiently
            var updatedGuilds = await UpdateAllGuildPointsAndLevels();

            if (updatedGuilds.Any())
            {
                Logger.Instance.Info($"✓ Successfully updated {updatedGuilds.Count} guilds:");
                foreach (var guild in updatedGuilds)
                {
                    Logger.Instance.Debug($"  - {guild.GuildName}: {guild.TotalPoints} points, Level {guild.NewLevel}, {guild.MemberCount} members");
                }
                return true;
            }

            return false;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Error in optimized guild point update: {ex.Message}");
            return false;
        }
    }

    #endregion

    #region Player Guild Data

    /// <summary>
    /// Player guild data model
    /// Model dữ liệu bang phái của người chơi
    /// </summary>
    public class PlayerGuildData
    {
        public int GuildId { get; set; }
        public string GuildName { get; set; } = string.Empty;
        public string PlayerName { get; set; } = string.Empty;
        public int MemberLevel { get; set; }
        public int GuildLevel { get; set; }
        public int MonPhucWord { get; set; }
        public int MonPhucMauSac { get; set; }
        public byte[] MonHuy { get; set; }
        public bool HasGuild => !string.IsNullOrEmpty(GuildName);
    }

    /// <summary>
    /// Get player guild data efficiently using FreeSql
    /// Lấy dữ liệu bang phái của người chơi hiệu quả bằng FreeSql
    /// </summary>
    /// <param name="characterName">Character name - Tên nhân vật</param>
    /// <returns>Player guild data or null if not in guild</returns>
    public static async Task<PlayerGuildData> GetPlayerGuildData(string characterName)
    {
        try
        {
            if (string.IsNullOrEmpty(characterName))
            {
                Logger.Instance.Error("GetPlayerGuildData: Character name cannot be null or empty");
                return null;
            }

            // Get guild member data first
            var guildMember = await _freeSql
                .Select<tbl_xwwl_guildmember>()
                .Where(a => a.fld_name == characterName)
                .FirstAsync();

            if (guildMember == null || string.IsNullOrEmpty(guildMember.g_name))
            {
                Logger.Instance.Debug($"Player {characterName} is not in any guild");
                return null;
            }

            // Get guild information
            var guild = await _freeSql
                .Select<tbl_xwwl_guild>()
                .Where(a => a.g_name == guildMember.g_name)
                .FirstAsync();

            if (guild == null)
            {
                Logger.Instance.Warning($"Guild {guildMember.g_name} not found for player {characterName}");
                return null;
            }

            // Create player guild data
            var playerGuildData = new PlayerGuildData
            {
                GuildId = guild.id,
                GuildName = guild.g_name,
                PlayerName = guildMember.fld_name,
                MemberLevel = guildMember.leve ?? 0,
                GuildLevel = guild.leve ?? 0,
                MonPhucWord = guild.monphucword ?? 0,
                MonPhucMauSac = guild.monphucmausac ?? 0,
                MonHuy = guild.monhuy
            };

            Logger.Instance.Debug($"✓ Loaded guild data for {characterName}: Guild {guild.g_name} (Level {guild.leve})");
            return playerGuildData;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Error getting player guild data for {characterName}: {ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// Get player guild data with single optimized query (alternative approach)
    /// Lấy dữ liệu bang phái với single query tối ưu (cách tiếp cận thay thế)
    /// </summary>
    /// <param name="characterName">Character name - Tên nhân vật</param>
    /// <returns>Player guild data or null if not in guild</returns>
    public static async Task<PlayerGuildData> GetPlayerGuildDataOptimized(string characterName)
    {
        try
        {
            if (string.IsNullOrEmpty(characterName))
            {
                Logger.Instance.Error("GetPlayerGuildDataOptimized: Character name cannot be null or empty");
                return null;
            }

            // Single query with JOIN to get all data at once
            var result = await _freeSql
                .Select<tbl_xwwl_guildmember, tbl_xwwl_guild>()
                .LeftJoin((gm, g) => gm.g_name == g.g_name)
                .Where((gm, g) => gm.fld_name == characterName && g.active == true)
                .FirstAsync((gm, g) => new
                {
                    GuildId = g.id,
                    GuildName = g.g_name,
                    PlayerName = gm.fld_name,
                    MemberLevel = gm.leve,
                    GuildLevel = g.leve,
                    MonPhucWord = g.monphucword,
                    MonPhucMauSac = g.monphucmausac,
                    MonHuy = g.monhuy
                });

            if (result == null || string.IsNullOrEmpty(result.GuildName))
            {
                Logger.Instance.Debug($"Player {characterName} is not in any guild");
                return null;
            }

            // Create player guild data
            var playerGuildData = new PlayerGuildData
            {
                GuildId = result.GuildId,
                GuildName = result.GuildName,
                PlayerName = result.PlayerName,
                MemberLevel = result.MemberLevel ?? 0,
                GuildLevel = result.GuildLevel ?? 0,
                MonPhucWord = result.MonPhucWord ?? 0,
                MonPhucMauSac = result.MonPhucMauSac ?? 0,
                MonHuy = result.MonHuy
            };

            Logger.Instance.Debug($"✓ Loaded guild data (optimized) for {characterName}: Guild {result.GuildName} (Level {result.GuildLevel})");
            return playerGuildData;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Error getting optimized player guild data for {characterName}: {ex.Message}");
            return null;
        }
    }

    #endregion

    #region Player Pet

    public static async Task<tbl_xwwl_cw> GetPlayerPet(long petSeries)
    {
        try
        {
            return await _freeSql
            .Select<tbl_xwwl_cw>()
            .Where(a => a.itmeid == petSeries)
            .FirstAsync();
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Error Getting player pet " + ex.Message);
            return null;
        }
    }

    public static async Task<bool> UpdatePlayerPet(string playerName, string petname, int zcd, long petSeries)
    {
        try
        {
            var result = await _freeSql
            .Update<tbl_xwwl_cw>()
            .Set(a => a.zrname == playerName)
            .Set(a => a.name == petname)
            .Set(a => a.fld_zcd == zcd)
            .Where(a => a.itmeid == petSeries)
            .ExecuteAffrowsAsync();
            return result > 0;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Error Updating player pet " + ex.Message);
            return false;
        }
    }

    #endregion

    #region Cache Management

    /// <summary>
    /// Refresh all cached data from database
    /// </summary>
    public static async Task<bool> RefreshAsync()
    {
        try
        {
            Logger.Instance.Info("Refreshing GameDb cached data...");

            // Reload guild members
            await LoadGuildMemberAsync();
            Logger.Instance.Info("✓ Guild members reloaded");

            // Reload guild rankings
            await LoadGuildRanking();
            Logger.Instance.Info("✓ Guild rankings reloaded");

            // Reload all rankings
            await LoadAllRanking();
            Logger.Instance.Info("✓ All rankings reloaded");

            Logger.Instance.Info("✓ GameDb refresh completed successfully");
            return true;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Failed to refresh GameDb: {ex.Message}");
            return false;
        }
    }

    #endregion

    #region siege

    public static async Task<int> TotalSiege()
    {
        try
        {
            return (int)await _freeSql
            .Select<thienmathancung_danhsach>()
            .CountAsync();
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Error Counting total siege " + ex.Message);
            return 0;
        }
    }

    public static async void RegisterSiege(string guildName)
    {
        try
        {
            await _freeSql
            .Insert(new thienmathancung_danhsach
            {
                bang_chiem_thanh = guildName,
                ngay_chiem_thanh = DateTime.Now.ToString("dd/MM/yyyy"),
                cong_thanh_cuonghoa_level = "0"
            })
            .ExecuteAffrowsAsync();
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Error Registering siege " + ex.Message);
        }
    }

    public static async Task<int> UpdateSiegeNotice(string SiegeMaster, int status)
    {
        try
        {
            return await _freeSql
            .Update<tbl_xwwl_guild>()
            .Set(a => a.thongbao_congthanh == status)
            .Where(a => a.lienminh_minhchu == SiegeMaster)
            .ExecuteAffrowsAsync();
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Error Updating siege notice " + ex.Message);
            return -1;
        }
    }

    #endregion

    #region Faction War

    public static async Task<List<eventtop>> FindTopPlayerFactionWar(int top)
    {
        try
        {
            return await _freeSql
            .Select<eventtop>()
            .Where(a => a.createdat == DateTime.Now.Date)
            .OrderByDescending(a => a.gietnguoisoluong)
            .OrderBy(a => a.tuvongsoluong)
            .Take(top)
            .ToListAsync();
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Error Finding top player faction war " + ex.Message);
            return new List<eventtop>();
        }
    }

    #endregion

    public static async Task<int> FindPkLog(string killer, string victim)
    {
        try
        {
            return (int)await _freeSql
            .Select<tbl_xwwl_pklog>()
            .Where(a => a.fld_killer == killer && a.fld_death == victim)
            .CountAsync();
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Error Finding pk log " + ex.Message);
            return 0;
        }
    }
    public static async Task<bool> UpdatePkLog(string killer, string victim, int num15, string killerGuild, string targetGuild)
    {
        try
        {
            // DBA.ExeSqlCommand($"UPDATE  TBL_XWWL_PKLog  SET  FLD_NUM=FLD_NUM+1,FLD_LASTTIME='{DateTime.Now}',FLD_WX=FLD_WX+{num15},FLD_KILLER_GUILD='{mythis.GuildName}',FLD_DEATH_GUILD='{targetPlayer.GuildName}'      WHERE      FLD_KILLER='{mythis.CharacterName}'      AND      FLD_DEATH      =      '{targetPlayer.CharacterName}'      ").GetAwaiter().GetResult();
            await _freeSql
            .Update<tbl_xwwl_pklog>()
            .Set(a => a.fld_num == a.fld_num + 1)
            .Set(a => a.fld_lasttime == DateTime.Now)
            .Set(a => a.fld_wx == a.fld_wx + num15)
            .Set(a => a.fld_killer_guild == killerGuild)
            .Set(a => a.fld_death_guild == targetGuild)
            .Where(a => a.fld_killer == killer && a.fld_death == victim)
            .ExecuteAffrowsAsync();
            return true;


        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Error Updating pk log " + ex.Message);
            return false;
        }
    }
    public static async Task<bool> InsertPkLog(string killer, string victim, int num15, string killerGuild, string victimGuild)
    {
        try
        {
            await _freeSql
            .Insert(new tbl_xwwl_pklog
            {
                fld_killer = killer,
                fld_killer_guild = killerGuild,
                fld_death = victim,
                fld_death_guild = victimGuild,
                fld_num = 1,
                fld_wx = num15,
                fld_lasttime = DateTime.Now
            })
            .ExecuteAffrowsAsync();
            return true;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Error Inserting pk log " + ex.Message);
            return false;
        }
    }

    public static async Task<bool> UpdatePetName(int itemId, string petName, int zcd)
    {
        try
        {
            await _freeSql
            .Update<tbl_xwwl_cw>()
            .Set(a => a.name == petName)
            .Set(a => a.fld_zcd == zcd)
            .Where(a => a.itmeid == itemId)
            .ExecuteAffrowsAsync();
            return true;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Error Updating pet name " + ex.Message);
            return false;
        }
    }

    public static async Task<bool> SetAdmin(string characterName, int adminLevel)
    {
        try
        {
            await _freeSql
            .Update<tbl_xwwl_char>()
            .Set(a => a.fld_j9 == adminLevel)
            .Where(a => a.fld_name == characterName)
            .ExecuteAffrowsAsync();
            return true;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Error Setting admin " + ex.Message);
            return false;
        }
    }

    public static async Task<bool> InsertPvpLog(string eventName, string playerA, string playerB, int playerAScore, int playerBScore, int playerAEscapes, int playerBEscapes, int playerAGold, int playerBGold, string result)
    {
        try
        {

            //DBA.ExeSqlCommand(string.Format("INSERT INTO TBL_XWWL_PVP (SanTapTen,A_NguoiChoi,B_NguoiChoi,AGietNguoiSoLuong,BGietNguoiSoLuong,A_ChayTronSoLan,B_ChayTronSoLan,AThuDuocNguyenBao,BThuDuocNguyenBao,TranhTaiKetQua)values({0},'{1}','{2}',{3},{4},{5},{6},{7},{8},'{9}')", 90, PlayerA.CharacterName, PlayerB.CharacterName, PlayerA.PVPScore, PlayerB.PVPScore, PlayerA.PVPEscapes, PlayerB.PVPEscapes, num, 0, PlayerA.CharacterName + " Chiến thắng"));

            await _freeSql
            .Insert(new tbl_xwwl_pvp
            {
                santapten = eventName,
                a_nguoichoi = playerA,
                b_nguoichoi = playerB,
                agietnguoisoluong = playerAScore,
                bgietnguoisoluong = playerBScore,
                a_chaytronsolan = playerAEscapes,
                b_chaytronsolan = playerBEscapes,
                athuduocnguyenbao = playerAGold,
                bthuduocnguyenbao = playerBGold,
                tranhtaiketqua = result
            })
            .ExecuteAffrowsAsync();
            return true;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Error Inserting pvp log " + ex.Message);
            return false;
        }
    }

    public static async Task<bool> RewardGuildWar(string guildName, int guildHonor, int guildTitle, int win, int lose, int draw)
    {
        try
        {
            await _freeSql
            .Update<tbl_xwwl_guild>()
            .Set(a => a.bangphaivohuan == guildHonor)
            .Set(a => a.thanhdanh == guildTitle)
            .Set(a => a.thang == win)
            .Set(a => a.thua == lose)
            .Set(a => a.hoa == draw)
            .Where(a => a.g_name == guildName)
            .ExecuteAffrowsAsync();
            return true;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Error Rewarding guild war " + ex.Message);
            return false;
        }

    }

    #region Attendance System

    /// <summary>
    /// Load player attendance progress
    /// </summary>
    public static async Task<PlayerAttendance> LoadPlayerAttendanceAsync(string playerName, int attendanceId)
    {
        try
        {
            if (_freeSql == null) return null;

            var record = await _freeSql.Select<tbl_player_attendance>()
                .Where(p => p.player_name == playerName && p.attendance_id == attendanceId)
                .FirstAsync();

            if (record == null) return null;

            return new PlayerAttendance
            {
                Id = record.id,
                PlayerName = record.player_name,
                AttendanceId = record.attendance_id,
                ReceivedDaysString = record.received_days ?? "",
                LastReceivedDate = record.last_received_date,
                CreatedDate = record.created_date,
                UpdatedDate = record.updated_date
            };
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Failed to load player attendance: {ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// Save hoặc update player attendance progress
    /// </summary>
    public static async Task<bool> SavePlayerAttendanceAsync(PlayerAttendance attendance)
    {
        try
        {
            if (_freeSql == null) return false;

            var existingRecord = await _freeSql.Select<tbl_player_attendance>()
                .Where(p => p.player_name == attendance.PlayerName && p.attendance_id == attendance.AttendanceId)
                .FirstAsync();

            if (existingRecord == null)
            {
                // Insert new record
                var newRecord = new tbl_player_attendance
                {
                    player_name = attendance.PlayerName,
                    attendance_id = attendance.AttendanceId,
                    received_days = attendance.ReceivedDaysString,
                    last_received_date = attendance.LastReceivedDate,
                    created_date = DateTime.Now,
                    updated_date = DateTime.Now
                };

                var result = await _freeSql.Insert(newRecord).ExecuteAffrowsAsync();
                return result > 0;
            }
            else
            {
                // Update existing record
                var result = await _freeSql.Update<tbl_player_attendance>()
                    .Set(p => p.received_days, attendance.ReceivedDaysString)
                    .Set(p => p.last_received_date, attendance.LastReceivedDate)
                    .Set(p => p.updated_date, DateTime.Now)
                    .Where(p => p.id == existingRecord.id)
                    .ExecuteAffrowsAsync();

                return result > 0;
            }
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Failed to save player attendance: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Update player received day - thêm ngày đã nhận thưởng
    /// </summary>
    public static async Task<bool> UpdatePlayerReceivedDayAsync(string playerName, int attendanceId, int dayNumber)
    {
        try
        {
            if (_freeSql == null) return false;

            var attendance = await LoadPlayerAttendanceAsync(playerName, attendanceId);
            if (attendance == null)
            {
                // Tạo mới nếu chưa có
                attendance = new PlayerAttendance
                {
                    PlayerName = playerName,
                    AttendanceId = attendanceId,
                    ReceivedDaysString = "",
                    CreatedDate = DateTime.Now,
                    UpdatedDate = DateTime.Now
                };
            }

            // Thêm ngày đã nhận
            attendance.AddReceivedDay(dayNumber);
            attendance.LastReceivedDate = DateTime.Now;

            return await SavePlayerAttendanceAsync(attendance);
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Failed to update player received day: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Kiểm tra player đã nhận thưởng ngày nào chưa
    /// </summary>
    public static async Task<bool> HasPlayerReceivedDayAsync(string playerName, int attendanceId, int dayNumber)
    {
        try
        {
            var attendance = await LoadPlayerAttendanceAsync(playerName, attendanceId);
            return attendance?.HasReceivedDay(dayNumber) ?? false;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Failed to check player received day: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Lấy số ngày từ lần login cuối cùng của player
    /// </summary>
    public static async Task<int> GetDaysSinceLastLoginAsync(string playerName)
    {
        try
        {
            if (_freeSql == null) return -1;

            var lastLoginRecord = await _freeSql.Select<loginrecord>()
                .Where(l => l.username == playerName)
                .OrderByDescending(l => l.thoigian)
                .FirstAsync();

            if (lastLoginRecord?.thoigian == null)
            {
                // Không tìm thấy record login, coi như đã lâu không login
                return 999; // Trả về số lớn để đảm bảo đủ điều kiện
            }

            var daysSince = (DateTime.Now - lastLoginRecord.thoigian.Value).Days;
            return daysSince;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Failed to get days since last login for {playerName}: {ex.Message}");
            return -1;
        }
    }

    /// <summary>
    /// Kiểm tra player có đủ điều kiện attendance 14 ngày không
    /// </summary>
    public static async Task<bool> IsEligibleForComeback14DayAsync(string playerName, int requiredDays = 14)
    {
        try
        {
            var daysSinceLogin = await GetDaysSinceLastLoginAsync(playerName);

            // Nếu có lỗi khi lấy thông tin, coi như không đủ điều kiện
            if (daysSinceLogin == -1) return false;

            // Kiểm tra đã đủ số ngày yêu cầu chưa
            return daysSinceLogin >= requiredDays;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Failed to check comeback eligibility for {playerName}: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Lấy danh sách tất cả attendance progress của player
    /// </summary>
    public static async Task<List<PlayerAttendance>> GetPlayerAllAttendanceAsync(string playerName)
    {
        try
        {
            if (_freeSql == null) return new List<PlayerAttendance>();

            var records = await _freeSql.Select<tbl_player_attendance>()
                .Where(p => p.player_name == playerName)
                .ToListAsync();

            return records.Select(record => new PlayerAttendance
            {
                Id = record.id,
                PlayerName = record.player_name,
                AttendanceId = record.attendance_id,
                ReceivedDaysString = record.received_days ?? "",
                LastReceivedDate = record.last_received_date,
                CreatedDate = record.created_date,
                UpdatedDate = record.updated_date
            }).ToList();
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Failed to get player all attendance: {ex.Message}");
            return new List<PlayerAttendance>();
        }
    }

    #endregion


    #region Cumulative Reward System

    /// <summary>
    /// Lấy danh sách milestone đã nhận của player cho template cụ thể
    /// </summary>
    public static async Task<List<int>> GetPlayerClaimedCumulativeRewardMilestones(string playerName, int templateId)
    {
        try
        {
            if (_freeSql == null) return new List<int>();

            var claimedMilestones = await _freeSql
                .Select<tbl_player_cumulative_reward_log>()
                .Where(log => log.player_name == playerName && log.template_id == templateId)
                .ToListAsync(log => log.milestone_number);

            return claimedMilestones;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Failed to get claimed cumulative reward milestones: {ex.Message}");
            return new List<int>();
        }
    }

    /// <summary>
    /// Lưu log nhận thưởng cumulative reward
    /// </summary>
    public static async Task<bool> SaveCumulativeRewardLog(string playerName, int templateId, int milestoneNumber, int cashSpentAtTime)
    {
        try
        {
            if (_freeSql == null) return false;

            await _freeSql.Insert(new tbl_player_cumulative_reward_log
            {
                player_name = playerName,
                template_id = templateId,
                milestone_number = milestoneNumber,
                cash_spent_at_time = cashSpentAtTime,
                received_date = DateTime.Now,
                created_date = DateTime.Now
            }).ExecuteAffrowsAsync();

            return true;
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"✗ Failed to save cumulative reward log: {ex.Message}");
            return false;
        }
    }

    #endregion
}
