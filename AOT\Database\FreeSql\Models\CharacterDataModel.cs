using System;

namespace HeroYulgang.Database.FreeSql.Models
{
    /// <summary>
    /// Character data model for saving character information to PostgreSQL
    /// Model dữ liệu nhân vật để lưu thông tin nhân vật vào PostgreSQL
    /// </summary>
    public class CharacterDataModel
    {
        // Basic character information - Thông tin cơ bản nhân vật
        public string AccountID { get; set; }
        public string CharacterName { get; set; }
        public int Player_Level { get; set; }
        public byte[] NewCharacterTemplate_CharacterTemplate_byte { get; set; }
        public int Player_Job { get; set; }
        public string CharacterExperience { get; set; }
        public int Player_Zx { get; set; }
        public int Player_Job_level { get; set; }

        // Position information - Thông tin vị trí
        public float PosX { get; set; }
        public float PosY { get; set; }
        public float PosZ { get; set; }
        public int MapID { get; set; }

        // Character stats - Chỉ số nhân vật
        public string Player_Money { get; set; }
        public int NhanVat_HP { get; set; }
        public int NhanVat_MP { get; set; }
        public int NhanVat_SP { get; set; }
        public int Player_WuXun { get; set; }
        public int NhanVatThienVaAc { get; set; }
        public int Player_Qigong_point { get; set; }

        // Binary data arrays - Dữ liệu nhị phân
        public byte[] GetWgCodesbyte { get; set; }
        public byte[] GetWEARITEMCodesbyte { get; set; }
        public byte[] GetFLD_ITEMCodesbyte { get; set; }
        public byte[] GetFLD_FASHION_ITEMCodesbyte { get; set; }
        public byte[] GetFLD_NTCITEMCodesbyte { get; set; }
        public byte[] GetQuestITEMCodesbyte { get; set; }
        public byte[] GetFLD_KONGFUCodesbyte { get; set; }
        public byte[] GetPersonalMedicinebyte { get; set; }
        public byte[] GetThoLinhPhubyte { get; set; }
        public byte[] GetNhiemVubyte { get; set; }
        public byte[] GetNhiemVuFinishbyte { get; set; }

        // Experience and skills - Kinh nghiệm và kỹ năng
        public int Player_ExpErience { get; set; }
        public int Character_KhinhCong { get; set; }
        public byte[] CharacterNameTemplate { get; set; }
        public int EquipmentDataVersion { get; set; }
        public int FLD_LoaiSanXuat { get; set; }
        public int FLD_TrinhDoSanXuat { get; set; }
        public byte[] GetPersonalMedicineNewbyte { get; set; }

        // Relationship information - Thông tin quan hệ
        public string FLD_Couple { get; set; }
        public int FLD_Couple_Love { get; set; }
        public string FLD_CoupleRing { get; set; }
        public int GiaiTruQuanHe_Countdown { get; set; }
        public int WhetherMarried { get; set; }
        public string NhanCuoiKhacChu { get; set; }

        // Advanced skills - Kỹ năng nâng cao
        public byte[] GetThangThienKhiCongCodesbyte { get; set; }
        public byte[] GetThangThienVoCongCodesbyte { get; set; }
        public int ThangThienLichLuyen_KinhNghiem { get; set; }
        public int ThangThienVoCong_DiemSo { get; set; }
        public int FLD_NUMBER_OPEN { get; set; }
        public int TitlePoints { get; set; }
        public DateTime? FLD_NgayKiNiemKetHon { get; set; }
        public int FLD_PVP_Piont { get; set; }

        // Time-based data - Dữ liệu theo thời gian
        public int RemainingTimeOfTrainingMap { get; set; }
        public int ActivityMapRemainingTime { get; set; }
        public int MatDi_VoHuan { get; set; }
        public int NhanVoHuan_MoiNgay { get; set; }
        public int RoseTitlePoints { get; set; }

        // Additional stats - Chỉ số bổ sung
        public int BanThuong_ThemVao_SinhMenh { get; set; }
        public string BanThuong_ThemVao_TanCong { get; set; }
        public string BanThuong_ThemVao_PhongThu { get; set; }
        public int BanThuong_ThemVao_NeTranh { get; set; }
        public int BanThuong_ThemVao_NoiCong { get; set; }
        public int BanThuong_ThemVao_TrungDich { get; set; }
        public int NumberOfRebirths { get; set; }
        public int BanThuong_ThemVao_CLVC { get; set; }
        public int BanThuong_ThemVao_PTVC { get; set; }
        public int BanThuong_ThemVao_KC { get; set; }
        public int BangPhai_DoCongHien { get; set; }
        public int Player_Whtb { get; set; }

        // Medicine and time data - Dữ liệu thuốc và thời gian
        public byte[] GetTitleDrugbyte { get; set; }
        public byte[] GetTimeMedicinebyte { get; set; }
        public int ThanNuVoCongDiemSo { get; set; }
        public string ClientSettings { get; set; }
        public string TheLucChien_PhePhai { get; set; }
        public byte[] GetEventBagCodesbyte { get; set; }
        public byte[] GetPhanKhiCongCodesbyte { get; set; }
    }
}
