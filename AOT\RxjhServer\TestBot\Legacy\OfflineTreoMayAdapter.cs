using System;
using RxjhServer.TestBot.Core;
using HeroYulgang.Helpers;

namespace RxjhServer.TestBot.Legacy
{
    /// <summary>
    /// Adapter để tích hợp hệ thống TestBot với hệ thống Offline_Treo_May hiện tại
    /// </summary>
    public static class OfflineTreoMayAdapter
    {
        /// <summary>
        /// Xử lý offline training với khả năng fallback
        /// </summary>
        /// <param name="player">Player instance</param>
        public static void ProcessOfflineTraining(Players player)
        {
            try
            {
                // Kiểm tra xem player có sử dụng hệ thống mới không
                if (ShouldUseNewSystem(player))
                {
                    // LogHelper.WriteLine(LogLevel.Info, $"[OfflineTreoMayAdapter] chế độ mới");
                    // Sử dụng TestBot system
                    var botManager = TestBotManager.Instance;
                    
                    // Kiểm tra xem đã có bot chưa
                    if (!botManager.HasActiveBot(player.SessionID))
                    {
                        // Tạo bot mới với config từ hệ thống cũ
                        var config = CreateConfigFromLegacySettings(player);
                        botManager.StartBot(player, config);
                    }
                    
                    // Process với hệ thống mới
                    botManager.ProcessOfflineTraining(player);
                }
                else
                {
                     LogHelper.WriteLine(LogLevel.Info, $"[OfflineTreoMayAdapter] chế độ cũ");
                    // Fallback về hệ thống cũ
                    Offline_Treo_May.OfflineTreoMay(player);
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"[OfflineTreoMayAdapter] Error processing offline training: {ex.Message}");
                
                // Fallback về hệ thống cũ nếu có lỗi
                try
                {
                    Offline_Treo_May.OfflineTreoMay(player);
                }
                catch (Exception fallbackEx)
                {
                    LogHelper.WriteLine(LogLevel.Error, $"[OfflineTreoMayAdapter] Fallback also failed: {fallbackEx.Message}");
                }
            }
        }
        
        /// <summary>
        /// Kiểm tra xem có nên sử dụng hệ thống mới không
        /// </summary>
        /// <param name="player">Player instance</param>
        /// <returns>True nếu nên sử dụng hệ thống mới</returns>
        private static bool ShouldUseNewSystem(Players player)
        {
            try
            {
                // Kiểm tra setting từ database hoặc config
                // Có thể dựa vào level, VIP status, hoặc setting cụ thể
                
                var useNewSystem = GetPlayerSetting(player, "UseTestBot", true);
                
                return useNewSystem;
            }
            catch
            {
                return false; // Default to old system if error
            }
        }
        
        /// <summary>
        /// Tạo BotConfig từ settings của hệ thống cũ
        /// </summary>
        /// <param name="player">Player instance</param>
        /// <returns>BotConfig</returns>
        private static BotConfig CreateConfigFromLegacySettings(Players player)
        {
            try
            {
                var config = new BotConfig
                {
                    // Basic settings
                    IsEnabled = player.Offline_TreoMay_Mode_ON_OFF == 1,
                    
                    // Movement settings
                    HomeX = player.Offline_TreoMay_ToaDo_X,
                    HomeY = player.Offline_TreoMay_ToaDo_Y,
                    HomeMapId = player.Offline_TreoMay_BanDo,
                    MaxRangeFromHome = World.Offline_TreoMay_PhamVi,
                    
                    // Combat settings
                    PreferredSkillId = player.OfflineTreoMaySkill_ID,
                    CombatRange = World.Offline_TreoMay_PhamVi,
                    
                    // Status settings
                    AutoHealEnabled = true,
                    HealThreshold = 0.7f,
                    AutoManaEnabled = true,
                    ManaThreshold = 0.2f,
                    
                    // Buff settings
                    AutoBuffEnabled = player.ChucNang_Auto_ThucHien == 1,
                    BuffInterval = 60000,
                    
                    // Item settings
                    ItemPickupEnabled = true,
                    ItemPickupRange = 50
                };
                
                // Job-specific settings
                if (player.Player_Job == 5) // Đại Phu
                {
                    config.AutoBuffIds.AddRange(new[] { 501502, 501601, 501501, 501603, 501602 });
                }
                
                LogHelper.WriteLine(LogLevel.Info, $"[OfflineTreoMayAdapter] Created config for player {player.CharacterName}");
                return config;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"[OfflineTreoMayAdapter] Error creating config: {ex.Message}");
                return BotConfig.CreateDefault(player);
            }
        }
        
        /// <summary>
        /// Lấy setting của player (placeholder)
        /// </summary>
        /// <param name="player">Player instance</param>
        /// <param name="settingName">Tên setting</param>
        /// <param name="defaultValue">Giá trị mặc định</param>
        /// <returns>Giá trị setting</returns>
        private static T GetPlayerSetting<T>(Players player, string settingName, T defaultValue)
        {
            try
            {
                // TODO: Implement actual player setting retrieval
                // Có thể lưu trong database hoặc file config
                return defaultValue;
            }
            catch
            {
                return defaultValue;
            }
        }
        
        /// <summary>
        /// Migrate player từ hệ thống cũ sang mới
        /// </summary>
        /// <param name="player">Player instance</param>
        /// <returns>True nếu migrate thành công</returns>
        public static bool MigrateToNewSystem(Players player)
        {
            try
            {
                var botManager = TestBotManager.Instance;
                
                // Tạo config từ settings cũ
                var config = CreateConfigFromLegacySettings(player);
                
                // Start bot mới
                if (botManager.StartBot(player, config))
                {
                    // Set flag để sử dụng hệ thống mới
                    SetPlayerSetting(player, "UseTestBot", true);
                    
                    LogHelper.WriteLine(LogLevel.Info, $"[OfflineTreoMayAdapter] Successfully migrated player {player.CharacterName} to new system");
                    return true;
                }
                
                return false;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"[OfflineTreoMayAdapter] Error migrating player: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// Migrate player từ hệ thống mới về cũ
        /// </summary>
        /// <param name="player">Player instance</param>
        /// <returns>True nếu migrate thành công</returns>
        public static bool MigrateToLegacySystem(Players player)
        {
            try
            {
                var botManager = TestBotManager.Instance;
                
                // Stop bot hiện tại
                botManager.StopBot(player.SessionID);
                
                // Set flag để sử dụng hệ thống cũ
                SetPlayerSetting(player, "UseTestBot", false);
                
                LogHelper.WriteLine(LogLevel.Info, $"[OfflineTreoMayAdapter] Successfully migrated player {player.CharacterName} to legacy system");
                return true;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"[OfflineTreoMayAdapter] Error migrating player to legacy: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// Set setting của player (placeholder)
        /// </summary>
        /// <param name="player">Player instance</param>
        /// <param name="settingName">Tên setting</param>
        /// <param name="value">Giá trị</param>
        private static void SetPlayerSetting<T>(Players player, string settingName, T value)
        {
            try
            {
                // TODO: Implement actual player setting storage
                // Có thể lưu trong database hoặc file config
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"[OfflineTreoMayAdapter] Error setting player setting: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Lấy thống kê hệ thống
        /// </summary>
        /// <returns>Thống kê string</returns>
        public static string GetSystemStats()
        {
            try
            {
                var botManager = TestBotManager.Instance;
                var newSystemCount = botManager.ActiveBotCount;
                var totalOfflineCount = World.OffLine_SoLuong;
                var legacySystemCount = totalOfflineCount - newSystemCount;
                
                return $"Total Offline: {totalOfflineCount} | New System: {newSystemCount} | Legacy System: {legacySystemCount}";
            }
            catch
            {
                return "System stats: Unknown";
            }
        }
    }
}
