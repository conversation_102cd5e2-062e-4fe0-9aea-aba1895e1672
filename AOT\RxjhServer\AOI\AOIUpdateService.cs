using HeroYulgang.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace RxjhServer.AOI
{
    /// <summary>
    /// Service responsible for updating Area of Interest (AOI) for players
    /// Handles efficient visibility updates using the grid-based AOI system
    /// </summary>
    public class AOIUpdateService
    {
        #region Singleton Pattern
        
        private static AOIUpdateService _instance;
        private static readonly object _lock = new object();
        
        /// <summary>
        /// Get the singleton instance of AOIUpdateService
        /// </summary>
        public static AOIUpdateService Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        _instance ??= new AOIUpdateService();
                    }
                }
                return _instance;
            }
        }
        
        #endregion
        
        #region Private Fields
        
        private readonly AOIManager _aoiManager;
        
        #endregion
        
        #region Constructor
        
        /// <summary>
        /// Private constructor for singleton pattern
        /// </summary>
        private AOIUpdateService()
        {
            _aoiManager = AOIManager.Instance;
            // LogHelper.WriteLine(LogLevel.Info, "AOIUpdateService initialized successfully");
        }
        
        #endregion
        
        #region Utility Methods
        
        /// <summary>
        /// Check if two positions are within AOI range
        /// </summary>
        /// <param name="x1">First position X</param>
        /// <param name="y1">First position Y</param>
        /// <param name="x2">Second position X</param>
        /// <param name="y2">Second position Y</param>
        /// <returns>True if positions are within AOI range</returns>
        private bool IsWithinAOI(float x1, float y1, float x2, float y2)
        {
            float dx = x2 - x1;
            float dy = y2 - y1;
            return (dx * dx + dy * dy) <= (AOIManager.AOI_RADIUS * AOIManager.AOI_RADIUS);
        }
        
        #endregion
        
        #region Player AOI Updates
        
        /// <summary>
        /// Update AOI for a specific player with caching optimization
        /// </summary>
        /// <param name="player">Player to update AOI for</param>
        public void UpdatePlayerAOI(Players player)
        {
            try
            {
                if (player == null || player.Client == null || !player.Client.Running)
                {
                    return;
                }

                // Get all grids within AOI range
                var aoiGrids = _aoiManager.GetAOIGrids(player.PosX, player.PosY, player.MapID);

                var visiblePlayers = new HashSet<Players>();
                var visibleNPCs = new HashSet<NpcClass>();
                var visibleItems = new HashSet<GroundItem>();

                // Collect entities from AOI grids with overlap-aware visibility
                var playerGrid = _aoiManager.GetGridByPosition(player.PosX, player.PosY, player.MapID);

                foreach (var grid in aoiGrids)
                {
                    // Collect visible players with overlap consideration
                    foreach (var otherPlayer in grid.Players)
                    {
                        if (otherPlayer != player &&
                            otherPlayer.Client != null &&
                            otherPlayer.Client.Running)
                        {
                            if (IsEntityVisibleWithOverlap(player, otherPlayer.PosX, otherPlayer.PosY, grid, playerGrid))
                            {
                                visiblePlayers.Add(otherPlayer);
                            }
                        }
                    }

                    // Collect visible NPCs with overlap consideration
                    foreach (var npc in grid.NPCs)
                    {
                        if (IsEntityVisibleWithOverlap(player, npc.Rxjh_X, npc.Rxjh_Y, grid, playerGrid))
                        {
                            visibleNPCs.Add(npc);
                        }
                    }

                    // Collect visible ground items with overlap consideration
                    foreach (var item in grid.GroundItems)
                    {
                        if (IsEntityVisibleWithOverlap(player, item.PosX, item.PosY, grid, playerGrid))
                        {
                            visibleItems.Add(item);
                        }
                    }
                }

                // Cache the results for future use
                var visiblePlayerIDs = new HashSet<int>(visiblePlayers.Select(p => p.SessionID));
                var visibleNPCIDs = new HashSet<int>(visibleNPCs.Select(n => n.NPC_SessionID));
                var visibleItemIDs = new HashSet<long>(visibleItems.Select(i => i.id));

                // AOICache.Instance.CachePlayerVisibility(player.SessionID, player.PosX, player.PosY, player.MapID,
                //     visiblePlayerIDs, visibleNPCIDs, visibleItemIDs);

                // Apply differential updates
                UpdatePlayerVisibility(player, visiblePlayers);
                UpdateNPCVisibility(player, visibleNPCs);
                UpdateItemVisibility(player, visibleItems);

                // CRITICAL: Invalidate cache of other players who should now see this player
                // This fixes the reciprocal visibility issue when a new player enters AOI
                // InvalidateOtherPlayersCache(player, visiblePlayers);

                // ENHANCED: Debug logging for grid transitions
                if (AOIConfiguration.Instance.EnableDebugLogging)
                {
                    var playerGrid1 = _aoiManager.GetGridByPosition(player.PosX, player.PosY, player.MapID);
                    var recentGridChange = playerGrid1 != null && HasRecentlyChangedGrid(playerGrid1);

                    LogHelper.WriteLine(LogLevel.Debug,
                        $"AOI updated for player {player.CharacterName} at ({player.PosX}, {player.PosY}) " +
                        $"Grid: ({playerGrid1?.GridX}, {playerGrid1?.GridY}) " +
                        $"Recent change: {recentGridChange} " +
                        $"Visible: {visiblePlayers.Count} players, {visibleNPCs.Count} NPCs, {visibleItems.Count} items");
                }

                //LogHelper.WriteLine(LogLevel.Info, $"AOI updated for player {player.CharacterName}: {visiblePlayers.Count} players, {visibleNPCs.Count} NPCs, {visibleItems.Count} items");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error updating player AOI: {ex.Message} {ex.StackTrace}");
            }
        }
        
        /// <summary>
        /// Update player visibility using differential updates
        /// </summary>
        /// <param name="player">Player to update visibility for</param>
        /// <param name="newVisiblePlayers">New set of visible players</param>
        private void UpdatePlayerVisibility(Players player, HashSet<Players> newVisiblePlayers)
        {
            try
            {
                if (player.PlayerList == null)
                {
                    return;
                }
                
                var currentVisible = new HashSet<Players>(player.PlayerList.Values);
                
                // Players to add (newly visible) - with race condition protection
                var toAdd = newVisiblePlayers.Except(currentVisible);
                foreach (var newPlayer in toAdd)
                {
                    if (newPlayer != null && newPlayer.Client != null && newPlayer.Client.Running)
                    {
                        if (!player.PlayerList.ContainsKey((newPlayer.OriginalServerID, newPlayer.SessionID)))
                        {
                            player.PlayerList[(newPlayer.OriginalServerID,newPlayer.SessionID)] = newPlayer;
                            player.UpdateCharacterData(newPlayer);

                            // Reciprocal update with null checks
                            if (newPlayer.PlayerList != null && !newPlayer.PlayerList.ContainsKey((player.OriginalServerID,player.SessionID)))
                            {
                                newPlayer.PlayerList[(player.OriginalServerID,player.SessionID)] = player;
                                newPlayer.UpdateCharacterData(player);
                            }
                        }
                    }
                }

                // ENHANCED: Players to remove with transition buffer - with race condition protection
                var toRemove = currentVisible.Except(newVisiblePlayers);
                foreach (var oldPlayer in toRemove)
                {
                    if (oldPlayer != null && player.PlayerList.ContainsKey((oldPlayer.OriginalServerID,oldPlayer.SessionID)))
                    {
                        // CRITICAL FIX: Check if player should be kept due to transition buffer
                        if (ShouldKeepPlayerDuringTransition(player, oldPlayer))
                        {
                            if (AOIConfiguration.Instance.EnableDebugLogging)
                            {
                                LogHelper.WriteLine(LogLevel.Debug, $"Keeping player {oldPlayer.CharacterName} in transition buffer for {player.CharacterName}");
                            }
                            continue; // Skip removal during transition
                        }

                        player.PlayerList.TryRemove((oldPlayer.OriginalServerID,oldPlayer.SessionID), out _ );
                        player.NotifyPlayerExit(player, oldPlayer);

                        // Reciprocal update with null checks
                        if (oldPlayer.PlayerList != null && oldPlayer.PlayerList.ContainsKey((player.OriginalServerID,player.SessionID)))
                        {
                            oldPlayer.PlayerList.TryRemove((player.OriginalServerID,player.SessionID), out _);
                            oldPlayer.NotifyPlayerExit(oldPlayer, player);
                        }

                        if (AOIConfiguration.Instance.EnableDebugLogging)
                        {
                            LogHelper.WriteLine(LogLevel.Debug, $"Removed player {oldPlayer.CharacterName} from {player.CharacterName}'s visibility");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error updating player visibility: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Update NPC visibility using differential updates
        /// </summary>
        /// <param name="player">Player to update NPC visibility for</param>
        /// <param name="newVisibleNPCs">New set of visible NPCs</param>
        private void UpdateNPCVisibility(Players player, HashSet<NpcClass> newVisibleNPCs)
        {
            try
            {
                if (player.NpcList == null)
                {
                    return;
                }
                
                var currentVisible = new HashSet<NpcClass>(player.NpcList.Values);
                
                // NPCs to add (newly visible)
                var toAdd = newVisibleNPCs.Except(currentVisible);
                var addDict = new Dictionary<int, NpcClass>();
                foreach (var newNPC in toAdd)
                {
                    if (!player.NpcList.ContainsKey(newNPC.NPC_SessionID))
                    {
                        player.NpcList.TryAdd(newNPC.NPC_SessionID, newNPC);
                        // No need to call PlayList_Add with AOI Grid system
                        addDict.Add(newNPC.NPC_SessionID, newNPC);
                    }
                }
                
                // ENHANCED: NPCs to remove with transition buffer - similar to player logic
                var toRemove = currentVisible.Except(newVisibleNPCs);
                var removeDict = new Dictionary<int, NpcClass>();
                foreach (var oldNPC in toRemove)
                {
                    if (player.NpcList.ContainsKey(oldNPC.NPC_SessionID))
                    {
                        // CRITICAL FIX: Check if NPC should be kept due to transition buffer
                        if (ShouldKeepNPCDuringTransition(player, oldNPC))
                        {
                            if (AOIConfiguration.Instance.EnableDebugLogging)
                            {
                                LogHelper.WriteLine(LogLevel.Debug, $"Keeping NPC {oldNPC.NPC_SessionID} in transition buffer for {player.CharacterName}");
                            }
                            continue; // Skip removal during transition
                        }

                        player.NpcList.TryRemove(oldNPC.NPC_SessionID, out _);
                        // No need to call PlayList_Remove with AOI Grid system
                        removeDict.Add(oldNPC.NPC_SessionID, oldNPC);

                        if (AOIConfiguration.Instance.EnableDebugLogging)
                        {
                            LogHelper.WriteLine(LogLevel.Debug, $"Removed NPC {oldNPC.NPC_SessionID} from {player.CharacterName}'s visibility");
                        }
                    }
                }
                
                // Send updates to client
                if (addDict.Count > 0)
                {
                    NpcClass.UpdateNPC_Spawn(addDict, player);
                }
                
                if (removeDict.Count > 0)
                {
                    NpcClass.UpdateNPC_Despawn(removeDict, player);
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error updating NPC visibility: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Update ground item visibility using differential updates
        /// </summary>
        /// <param name="player">Player to update item visibility for</param>
        /// <param name="newVisibleItems">New set of visible ground items</param>
        private void UpdateItemVisibility(Players player, HashSet<GroundItem> newVisibleItems)
        {
            try
            {
                if (player.ListOfGroundItems == null)
                {
                    return;
                }
                
                var currentVisible = new HashSet<GroundItem>(player.ListOfGroundItems.Values);
                
                // Items to add (newly visible)
                var toAdd = newVisibleItems.Except(currentVisible);
                var addDict = new Dictionary<long, GroundItem>();
                foreach (var newItem in toAdd)
                {
                    if (!player.ListOfGroundItems.ContainsKey(newItem.id))
                    {
                        player.ListOfGroundItems.TryAdd(newItem.id, newItem);
                        // No need to manage PlayList with AOI Grid system
                        // Ground items use AOI Grid for player tracking
                        addDict.Add(newItem.id, newItem);
                    }
                }
                
                // ENHANCED: Items to remove with transition buffer - similar to player/NPC logic
                var toRemove = currentVisible.Except(newVisibleItems);
                foreach (var oldItem in toRemove)
                {
                    if (player.ListOfGroundItems.ContainsKey(oldItem.id))
                    {
                        // CRITICAL FIX: Check if item should be kept due to transition buffer
                        if (ShouldKeepItemDuringTransition(player, oldItem))
                        {
                            if (AOIConfiguration.Instance.EnableDebugLogging)
                            {
                                LogHelper.WriteLine(LogLevel.Debug, $"Keeping Ground Item {oldItem.id} in transition buffer for {player.CharacterName}");
                            }
                            continue; // Skip removal during transition
                        }

                        player.ListOfGroundItems.TryRemove(oldItem.id, out _);
                        player.RemoveGroundItem(oldItem.id);

                        if (AOIConfiguration.Instance.EnableDebugLogging)
                        {
                            LogHelper.WriteLine(LogLevel.Debug, $"Removed Ground Item {oldItem.id} from {player.CharacterName}'s visibility");
                        }
                    }
                }
                
                // Send new items to client
                if (addDict.Count > 0)
                {
                    player.SendGroundItem(addDict);
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error updating item visibility: {ex.Message}");
            }
        }
        
        #endregion
        
        #region Batch Updates
        
        /// <summary>
        /// Update AOI for multiple players efficiently
        /// </summary>
        /// <param name="players">List of players to update</param>
        public void BatchUpdateAOI(List<Players> players)
        {
            try
            {
                if (players == null || players.Count == 0)
                {
                    return;
                }
                
                // Group players by grid for efficient processing
                var playersByGrid = players
                    .Where(p => p != null && p.Client != null && p.Client.Running)
                    .GroupBy(p => _aoiManager.GetGridByPosition(p.PosX, p.PosY, p.MapID))
                    .Where(g => g.Key != null);
                
                foreach (var gridGroup in playersByGrid)
                {
                    var grid = gridGroup.Key;
                    
                    // Only update if grid is dirty or has been a while since last update
                    if (grid.IsDirty || DateTime.Now - grid.LastUpdate > TimeSpan.FromSeconds(5))
                    {
                        foreach (var player in gridGroup)
                        {
                            UpdatePlayerAOI(player);
                        }
                        
                        grid.MarkClean();
                    }
                }
                
                LogHelper.WriteLine(LogLevel.Info, $"Batch AOI update completed for {players.Count} players");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error in batch AOI update: {ex.Message}");
            }
        }
        
        #endregion

        #region Optimization Helper Methods

       

        /// <summary>
        /// Get distance with caching optimization and grid transition awareness
        /// </summary>
        private float GetCachedDistance(float x1, float y1, float x2, float y2)
        {
            try
            {
                var distance = (float)Math.Sqrt(Math.Pow(x2 - x1, 2) + Math.Pow(y2 - y1, 2));

                return distance;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error calculating cached distance: {ex.Message}");
                // Fallback to direct calculation
                return (float)Math.Sqrt(Math.Pow(x2 - x1, 2) + Math.Pow(y2 - y1, 2));
            }
        }

        /// <summary>
        /// Enhanced distance calculation that considers grid boundaries and transitions
        /// </summary>
        /// <param name="player">Player viewing the entity</param>
        /// <param name="entityX">Entity X position</param>
        /// <param name="entityY">Entity Y position</param>
        /// <param name="playerGrid">Player's current grid</param>
        /// <param name="entityGrid">Entity's current grid</param>
        /// <returns>Effective distance considering grid transitions</returns>
        private float GetEffectiveDistance(Players player, float entityX, float entityY, AOIGrid playerGrid, AOIGrid entityGrid)
        {
            try
            {
                // Basic distance calculation
                var basicDistance = GetCachedDistance(player.PosX, player.PosY, entityX, entityY);

                // If in same grid, return basic distance
                if (playerGrid == entityGrid)
                {
                    return basicDistance;
                }

                // If grids are not adjacent, return basic distance
                if (!IsInTransitionZone(playerGrid, entityGrid))
                {
                    return basicDistance;
                }

                // ENHANCED: For adjacent grids, consider grid boundary effects
                var adjustedDistance = basicDistance;

                // If player recently changed grids, reduce effective distance slightly
                if (HasRecentlyChangedGrid(playerGrid))
                {
                    adjustedDistance = basicDistance * 0.95f; // 5% reduction for smooth transition

                    if (AOIConfiguration.Instance.EnableDebugLogging)
                    {
                        LogHelper.WriteLine(LogLevel.Debug, $"Adjusted distance for recent grid change: {basicDistance} -> {adjustedDistance}");
                    }
                }

                // If entity is near grid boundary, reduce effective distance
                if (IsEntityNearGridBoundary(entityX, entityY, entityGrid, AOIManager.OVERLAP_SIZE / 2))
                {
                    adjustedDistance = Math.Min(adjustedDistance, basicDistance * 0.9f); // Additional 10% reduction

                    if (AOIConfiguration.Instance.EnableDebugLogging)
                    {
                        LogHelper.WriteLine(LogLevel.Debug, $"Adjusted distance for entity near boundary: {basicDistance} -> {adjustedDistance}");
                    }
                }

                return adjustedDistance;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error calculating effective distance: {ex.Message}");
                return GetCachedDistance(player.PosX, player.PosY, entityX, entityY);
            }
        }

        /// <summary>
        /// Optimized batch update using thread pool
        /// </summary>
        public void BatchUpdatePlayersOptimized(List<Players> players)
        {
            try
            {
                if (players == null || players.Count == 0)
                {
                    return;
                }

                // Group players by grid for efficient processing
                var playersByGrid = players
                    .Where(p => p != null && p.Client != null && p.Client.Running)
                    .GroupBy(p => _aoiManager.GetGridByPosition(p.PosX, p.PosY, p.MapID))
                    .Where(g => g.Key != null)
                    .ToList();

                // Process each grid group in thread pool
                foreach (var gridGroup in playersByGrid)
                {
                    var grid = gridGroup.Key;
                    var gridPlayers = gridGroup.ToList();

                    // Only update if grid is dirty or hasn't been updated recently
                    if (grid.IsDirty || DateTime.Now - grid.LastUpdate > TimeSpan.FromSeconds(AOIConfiguration.Instance.MaxUpdateInterval))
                    {
                        foreach (var player in gridPlayers)
                        {
                            UpdatePlayerAOI(player);
                        }
                        grid.MarkClean();
                    }
                }

                LogHelper.WriteLine(LogLevel.Info, $"Optimized batch AOI update queued for {players.Count} players across {playersByGrid.Count} grids");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error in optimized batch AOI update: {ex.Message}");
            }
        }

        /// <summary>
        /// Update player AOI specifically for movement events (optimized)
        /// </summary>
        public void UpdatePlayerMovementAOI(Players player)
        {
            try
            {
                if (player == null || player.Client == null || !player.Client.Running)
                {
                    return;
                }

                // Quick movement-specific AOI update
                var grid = _aoiManager.GetGridByPosition(player.PosX, player.PosY, player.MapID);
                if (grid == null)
                {
                    return;
                }

                // Check if player recently changed grids (configurable window)
                var recentGridChange = DateTime.Now - grid.LastUpdate < TimeSpan.FromMilliseconds(AOIConfiguration.Instance.GridChangeWindowMs);

                // Update immediately if:
                // 1. Grid is dirty (entities added/removed)
                // 2. Player recently changed grids
                // 3. Haven't updated for more than 1 second
                // 4. Grid was forced to update (LastUpdate = MinValue)
                if (grid.IsDirty ||
                    recentGridChange ||
                    DateTime.Now - grid.LastUpdate > TimeSpan.FromSeconds(1) ||
                    grid.LastUpdate == DateTime.MinValue)
                {
                    // Use immediate update for movement
                    UpdatePlayerAOI(player);
                    grid.MarkClean();
                    //LogHelper.WriteLine(LogLevel.Info, $"Movement AOI update completed for player {player.CharacterName} (Grid: {grid.GridX},{grid.GridY})");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error in movement AOI update for player {player?.CharacterName}: {ex.Message}");
            }
        }

        /// <summary>
        /// Force immediate AOI update for a player (bypasses all throttling and caching)
        /// Use this when player changes grids or when immediate visibility update is critical
        /// </summary>
        public void ForceUpdatePlayerAOI(Players player)
        {
            try
            {
                if (player == null || player.Client == null || !player.Client.Running)
                {
                    return;
                }

                // ENHANCED: Debug logging for forced updates (usually grid changes)
                var grid = _aoiManager.GetGridByPosition(player.PosX, player.PosY, player.MapID);
                if (AOIConfiguration.Instance.EnableDebugLogging)
                {
                    LogHelper.WriteLine(LogLevel.Debug,
                        $"FORCE UPDATE triggered for player {player.CharacterName} at ({player.PosX}, {player.PosY}) " +
                        $"Grid: ({grid?.GridX}, {grid?.GridY}) " +
                        $"Grid dirty: {grid?.IsDirty} " +
                        $"Last update: {grid?.LastUpdate}");
                }

                // Force immediate update without any checks
                UpdatePlayerAOI(player);

                // Mark current grid as clean
                grid?.MarkClean();

                if (AOIConfiguration.Instance.EnableDebugLogging)
                {
                    LogHelper.WriteLine(LogLevel.Debug, $"Forced AOI update completed for player {player.CharacterName}");
                }
                else
                {
                    LogHelper.WriteLine(LogLevel.Debug, $"Forced AOI update completed for player {player.CharacterName}");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error in forced AOI update for player {player?.CharacterName}: {ex.Message}");
            }
        }

        /// <summary>
        /// Check if an entity is visible considering overlap zones for smooth transitions
        /// </summary>
        /// <param name="player">Player viewing the entity</param>
        /// <param name="entityX">Entity X position</param>
        /// <param name="entityY">Entity Y position</param>
        /// <param name="entityGrid">Grid containing the entity</param>
        /// <param name="playerGrid">Grid containing the player</param>
        /// <returns>True if entity should be visible</returns>
        private bool IsEntityVisibleWithOverlap(Players player, float entityX, float entityY, AOIGrid entityGrid, AOIGrid playerGrid)
        {
            try
            {
                // ENHANCED: Use effective distance calculation that considers grid transitions
                var distance = GetEffectiveDistance(player, entityX, entityY, playerGrid, entityGrid);

                // ENHANCED: Use extended AOI radius for grid transitions to prevent flickering
                var effectiveAOIRadius = AOIManager.AOI_RADIUS;

                // If player recently changed grids, use extended radius for smoother transition
                if (playerGrid != null && HasRecentlyChangedGrid(playerGrid))
                {
                    effectiveAOIRadius = AOIManager.AOI_RADIUS + (AOIManager.OVERLAP_SIZE / 2);
                    if (AOIConfiguration.Instance.EnableDebugLogging)
                    {
                        LogHelper.WriteLine(LogLevel.Debug, $"Using extended AOI radius {effectiveAOIRadius} for player {player.CharacterName} due to recent grid change");
                    }
                }

                if (distance > effectiveAOIRadius)
                {
                    return false;
                }

                // If entity is in same grid, it's visible (within AOI radius)
                if (entityGrid == playerGrid)
                {
                    return true;
                }

                // Use overlap zones if enabled in configuration
                if (AOIConfiguration.Instance.UseOverlapZones &&
                    playerGrid != null && entityGrid != null)
                {
                    // ENHANCED: Check if grids are adjacent OR if entity is in transition zone
                    bool isAdjacentOrTransition = playerGrid.AdjacentGrids.Contains(entityGrid) ||
                                                  IsInTransitionZone(playerGrid, entityGrid);

                    if (isAdjacentOrTransition)
                    {
                        // Check if entity is in the overlap zone of its grid
                        // This provides smoother transitions when moving between grids
                        if (entityGrid.ContainsPositionWithOverlap(entityX, entityY))
                        {
                            if (AOIConfiguration.Instance.EnableDebugLogging)
                            {
                                LogHelper.WriteLine(LogLevel.Debug, $"Entity visible in overlap zone - Player: {player.CharacterName}, Entity: ({entityX}, {entityY})");
                            }
                            return true;
                        }

                        // Also check if player is near the edge toward this entity's grid
                        // This ensures entities become visible before player crosses grid boundary
                        var distanceToEntityGrid = GetDistanceToGrid(player.PosX, player.PosY, entityGrid);
                        if (distanceToEntityGrid <= AOIManager.OVERLAP_SIZE)
                        {
                            if (AOIConfiguration.Instance.EnableDebugLogging)
                            {
                                LogHelper.WriteLine(LogLevel.Debug, $"Entity visible due to player near grid edge - Player: {player.CharacterName}, Distance to grid: {distanceToEntityGrid}");
                            }
                            return true;
                        }

                        // ENHANCED: Additional check for entities near grid boundaries
                        if (IsEntityNearGridBoundary(entityX, entityY, entityGrid, AOIManager.OVERLAP_SIZE / 2))
                        {
                            if (AOIConfiguration.Instance.EnableDebugLogging)
                            {
                                LogHelper.WriteLine(LogLevel.Debug, $"Entity visible due to being near grid boundary - Entity: ({entityX}, {entityY})");
                            }
                            return true;
                        }
                    }
                }

                // Default to distance-based visibility with effective radius
                return distance <= effectiveAOIRadius;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error in overlap visibility check: {ex.Message}");
                // Fallback to simple distance check
                return GetCachedDistance(player.PosX, player.PosY, entityX, entityY) <= AOIManager.AOI_RADIUS;
            }
        }

        /// <summary>
        /// Check if a player should be kept during grid transition to prevent flickering
        /// </summary>
        /// <param name="viewingPlayer">Player who is viewing</param>
        /// <param name="targetPlayer">Player being viewed</param>
        /// <returns>True if player should be kept during transition</returns>
        private bool ShouldKeepPlayerDuringTransition(Players viewingPlayer, Players targetPlayer)
        {
            try
            {
                // Get grids for both players
                var viewingGrid = _aoiManager.GetGridByPosition(viewingPlayer.PosX, viewingPlayer.PosY, viewingPlayer.MapID);
                var targetGrid = _aoiManager.GetGridByPosition(targetPlayer.PosX, targetPlayer.PosY, targetPlayer.MapID);

                if (viewingGrid == null || targetGrid == null)
                    return false;

                // If either player recently changed grids, apply transition buffer
                bool viewingPlayerRecentChange = HasRecentlyChangedGrid(viewingGrid);
                bool targetPlayerRecentChange = HasRecentlyChangedGrid(targetGrid);

                if (viewingPlayerRecentChange || targetPlayerRecentChange)
                {
                    // Check if players are still within extended AOI range
                    var distance = GetCachedDistance(viewingPlayer.PosX, viewingPlayer.PosY, targetPlayer.PosX, targetPlayer.PosY);
                    var extendedRadius = AOIManager.AOI_RADIUS + (AOIManager.OVERLAP_SIZE / 2);

                    if (distance <= extendedRadius)
                    {
                        if (AOIConfiguration.Instance.EnableDebugLogging)
                        {
                            LogHelper.WriteLine(LogLevel.Debug, $"Keeping player {targetPlayer.CharacterName} for {viewingPlayer.CharacterName} - distance: {distance}, extended radius: {extendedRadius}");
                        }
                        return true;
                    }
                }

                // Check if grids are adjacent (diagonal adjacency counts)
                var gridXDiff = Math.Abs(viewingGrid.GridX - targetGrid.GridX);
                var gridYDiff = Math.Abs(viewingGrid.GridY - targetGrid.GridY);

                if (gridXDiff <= 1 && gridYDiff <= 1 && (gridXDiff + gridYDiff) > 0)
                {
                    // For adjacent grids, check if target is near boundary
                    if (IsEntityNearGridBoundary(targetPlayer.PosX, targetPlayer.PosY, targetGrid, AOIManager.OVERLAP_SIZE / 3))
                    {
                        var distance = GetCachedDistance(viewingPlayer.PosX, viewingPlayer.PosY, targetPlayer.PosX, targetPlayer.PosY);
                        if (distance <= AOIManager.AOI_RADIUS + 100) // Small buffer
                        {
                            if (AOIConfiguration.Instance.EnableDebugLogging)
                            {
                                LogHelper.WriteLine(LogLevel.Debug, $"Keeping adjacent player {targetPlayer.CharacterName} near boundary for {viewingPlayer.CharacterName}");
                            }
                            return true;
                        }
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error checking transition buffer: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Check if an NPC should be kept during grid transition to prevent flickering
        /// </summary>
        /// <param name="viewingPlayer">Player who is viewing</param>
        /// <param name="targetNPC">NPC being viewed</param>
        /// <returns>True if NPC should be kept during transition</returns>
        private bool ShouldKeepNPCDuringTransition(Players viewingPlayer, NpcClass targetNPC)
        {
            try
            {
                // Get grids for player and NPC
                var viewingGrid = _aoiManager.GetGridByPosition(viewingPlayer.PosX, viewingPlayer.PosY, viewingPlayer.MapID);
                var npcGrid = _aoiManager.GetGridByPosition(targetNPC.Rxjh_X, targetNPC.Rxjh_Y, targetNPC.Rxjh_Map);

                if (viewingGrid == null || npcGrid == null)
                    return false;

                // If player recently changed grids, apply transition buffer
                bool viewingPlayerRecentChange = HasRecentlyChangedGrid(viewingGrid);

                if (viewingPlayerRecentChange)
                {
                    // Check if NPC is still within extended AOI range
                    var distance = GetCachedDistance(viewingPlayer.PosX, viewingPlayer.PosY, targetNPC.Rxjh_X, targetNPC.Rxjh_Y);
                    var extendedRadius = AOIManager.AOI_RADIUS + (AOIManager.OVERLAP_SIZE / 2);

                    if (distance <= extendedRadius)
                    {
                        if (AOIConfiguration.Instance.EnableDebugLogging)
                        {
                            LogHelper.WriteLine(LogLevel.Debug, $"Keeping NPC {targetNPC.NPC_SessionID} for {viewingPlayer.CharacterName} - distance: {distance}, extended radius: {extendedRadius}");
                        }
                        return true;
                    }
                }

                // Check if grids are adjacent and NPC is near boundary
                if (IsInTransitionZone(viewingGrid, npcGrid))
                {
                    if (IsEntityNearGridBoundary(targetNPC.Rxjh_X, targetNPC.Rxjh_Y, npcGrid, AOIManager.OVERLAP_SIZE / 3))
                    {
                        var distance = GetCachedDistance(viewingPlayer.PosX, viewingPlayer.PosY, targetNPC.Rxjh_X, targetNPC.Rxjh_Y);
                        if (distance <= AOIManager.AOI_RADIUS + 100) // Small buffer
                        {
                            if (AOIConfiguration.Instance.EnableDebugLogging)
                            {
                                LogHelper.WriteLine(LogLevel.Debug, $"Keeping adjacent NPC {targetNPC.NPC_SessionID} near boundary for {viewingPlayer.CharacterName}");
                            }
                            return true;
                        }
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error checking NPC transition buffer: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Check if a ground item should be kept during grid transition to prevent flickering
        /// </summary>
        /// <param name="viewingPlayer">Player who is viewing</param>
        /// <param name="targetItem">Ground item being viewed</param>
        /// <returns>True if item should be kept during transition</returns>
        private bool ShouldKeepItemDuringTransition(Players viewingPlayer, GroundItem targetItem)
        {
            try
            {
                // Get grids for player and item
                var viewingGrid = _aoiManager.GetGridByPosition(viewingPlayer.PosX, viewingPlayer.PosY, viewingPlayer.MapID);
                var itemGrid = _aoiManager.GetGridByPosition(targetItem.PosX, targetItem.PosY, targetItem.MapID);

                if (viewingGrid == null || itemGrid == null)
                    return false;

                // If player recently changed grids, apply transition buffer
                bool viewingPlayerRecentChange = HasRecentlyChangedGrid(viewingGrid);

                if (viewingPlayerRecentChange)
                {
                    // Check if item is still within extended AOI range
                    var distance = GetCachedDistance(viewingPlayer.PosX, viewingPlayer.PosY, targetItem.PosX, targetItem.PosY);
                    var extendedRadius = AOIManager.AOI_RADIUS + (AOIManager.OVERLAP_SIZE / 2);

                    if (distance <= extendedRadius)
                    {
                        if (AOIConfiguration.Instance.EnableDebugLogging)
                        {
                            LogHelper.WriteLine(LogLevel.Debug, $"Keeping Ground Item {targetItem.id} for {viewingPlayer.CharacterName} - distance: {distance}, extended radius: {extendedRadius}");
                        }
                        return true;
                    }
                }

                // Check if grids are adjacent and item is near boundary
                if (IsInTransitionZone(viewingGrid, itemGrid))
                {
                    if (IsEntityNearGridBoundary(targetItem.PosX, targetItem.PosY, itemGrid, AOIManager.OVERLAP_SIZE / 3))
                    {
                        var distance = GetCachedDistance(viewingPlayer.PosX, viewingPlayer.PosY, targetItem.PosX, targetItem.PosY);
                        if (distance <= AOIManager.AOI_RADIUS + 100) // Small buffer
                        {
                            if (AOIConfiguration.Instance.EnableDebugLogging)
                            {
                                LogHelper.WriteLine(LogLevel.Debug, $"Keeping adjacent Ground Item {targetItem.id} near boundary for {viewingPlayer.CharacterName}");
                            }
                            return true;
                        }
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error checking item transition buffer: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Check if player has recently changed grids (within transition buffer time)
        /// </summary>
        /// <param name="currentGrid">Current grid of the player</param>
        /// <returns>True if player recently changed grids</returns>
        private bool HasRecentlyChangedGrid(AOIGrid currentGrid)
        {
            try
            {
                // Check if grid was recently updated (indicating a grid change)
                var timeSinceGridUpdate = DateTime.Now - currentGrid.LastUpdate;
                return timeSinceGridUpdate.TotalMilliseconds <= AOIConfiguration.Instance.GridChangeWindowMs;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Check if entity is in transition zone between grids
        /// </summary>
        /// <param name="playerGrid">Player's current grid</param>
        /// <param name="entityGrid">Entity's current grid</param>
        /// <returns>True if entity is in transition zone</returns>
        private bool IsInTransitionZone(AOIGrid playerGrid, AOIGrid entityGrid)
        {
            try
            {
                // Calculate if grids are diagonally adjacent (not just orthogonally)
                var gridXDiff = Math.Abs(playerGrid.GridX - entityGrid.GridX);
                var gridYDiff = Math.Abs(playerGrid.GridY - entityGrid.GridY);

                // Consider diagonal adjacency as transition zone
                return gridXDiff <= 1 && gridYDiff <= 1 && (gridXDiff + gridYDiff) > 0;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Check if entity is near the boundary of its grid
        /// </summary>
        /// <param name="entityX">Entity X position</param>
        /// <param name="entityY">Entity Y position</param>
        /// <param name="grid">Grid containing the entity</param>
        /// <param name="boundaryThreshold">Distance threshold from boundary</param>
        /// <returns>True if entity is near grid boundary</returns>
        private bool IsEntityNearGridBoundary(float entityX, float entityY, AOIGrid grid, float boundaryThreshold)
        {
            try
            {
                // Check distance to each edge of the grid
                var distanceToLeftEdge = entityX - grid.MinX;
                var distanceToRightEdge = grid.MaxX - entityX;
                var distanceToTopEdge = entityY - grid.MinY;
                var distanceToBottomEdge = grid.MaxY - entityY;

                // Return true if entity is within threshold distance of any edge
                return distanceToLeftEdge <= boundaryThreshold ||
                       distanceToRightEdge <= boundaryThreshold ||
                       distanceToTopEdge <= boundaryThreshold ||
                       distanceToBottomEdge <= boundaryThreshold;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Enable debug logging for AOI system to help diagnose grid transition issues
        /// </summary>
        /// <param name="enable">True to enable debug logging</param>
        public void EnableDebugLogging(bool enable = true)
        {
            AOIConfiguration.Instance.EnableDebugLogging = enable;
            LogHelper.WriteLine(LogLevel.Info, $"AOI Debug logging {(enable ? "enabled" : "disabled")}");
        }

        /// <summary>
        /// Test NPC action broadcasting to verify cross-grid visibility
        /// </summary>
        /// <param name="testPlayer">Player to test with</param>
        /// <returns>Test results summary</returns>
        public string TestNPCActionBroadcasting(Players testPlayer)
        {
            try
            {
                if (testPlayer == null)
                    return "Test failed: Player is null";

                var results = new System.Text.StringBuilder();
                results.AppendLine("=== NPC Action Broadcasting Test ===");

                // Get current grid
                var currentGrid = _aoiManager.GetGridByPosition(testPlayer.PosX, testPlayer.PosY, testPlayer.MapID);
                if (currentGrid == null)
                {
                    return "Test failed: Could not get player grid";
                }

                results.AppendLine($"Player: {testPlayer.CharacterName}");
                results.AppendLine($"Position: ({testPlayer.PosX}, {testPlayer.PosY})");
                results.AppendLine($"Current Grid: ({currentGrid.GridX}, {currentGrid.GridY})");

                // Test NPCs in AOI range
                var aoiGrids = _aoiManager.GetAOIGrids(testPlayer.PosX, testPlayer.PosY, testPlayer.MapID);
                results.AppendLine($"AOI Grids Count: {aoiGrids.Count}");

                int npcsInRange = 0;
                int npcsCanBroadcast = 0;

                foreach (var grid in aoiGrids)
                {
                    foreach (var npc in grid.NPCs)
                    {
                        if (IsEntityVisibleWithOverlap(testPlayer, npc.Rxjh_X, npc.Rxjh_Y, grid, currentGrid))
                        {
                            npcsInRange++;

                            // Test if NPC can broadcast to this player
                            var nearbyPlayers = npc.GetNearbyPlayers();
                            if (nearbyPlayers.Contains(testPlayer))
                            {
                                npcsCanBroadcast++;
                            }

                            var distance = GetCachedDistance(testPlayer.PosX, testPlayer.PosY, npc.Rxjh_X, npc.Rxjh_Y);
                            results.AppendLine($"  NPC {npc.NPC_SessionID}: Distance={distance:F1}, CanBroadcast={nearbyPlayers.Contains(testPlayer)}");
                        }
                    }
                }

                results.AppendLine($"NPCs in visibility range: {npcsInRange}");
                results.AppendLine($"NPCs that can broadcast to player: {npcsCanBroadcast}");

                if (npcsInRange == npcsCanBroadcast)
                {
                    results.AppendLine("✅ SUCCESS: All visible NPCs can broadcast actions to player");
                }
                else
                {
                    results.AppendLine($"❌ ISSUE: {npcsInRange - npcsCanBroadcast} visible NPCs cannot broadcast to player");
                }

                results.AppendLine("=== Test Completed ===");

                return results.ToString();
            }
            catch (Exception ex)
            {
                return $"Test failed with error: {ex.Message}";
            }
        }

        /// <summary>
        /// Test method to verify grid transition fixes
        /// </summary>
        /// <param name="testPlayer">Player to test with</param>
        /// <returns>Test results summary</returns>
        public string TestGridTransitionFix(Players testPlayer)
        {
            try
            {
                if (testPlayer == null)
                    return "Test failed: Player is null";

                var results = new System.Text.StringBuilder();
                results.AppendLine("=== AOI Grid Transition Fix Test ===");

                // Get current grid
                var currentGrid = _aoiManager.GetGridByPosition(testPlayer.PosX, testPlayer.PosY, testPlayer.MapID);
                if (currentGrid == null)
                {
                    return "Test failed: Could not get player grid";
                }

                results.AppendLine($"Player: {testPlayer.CharacterName}");
                results.AppendLine($"Position: ({testPlayer.PosX}, {testPlayer.PosY})");
                results.AppendLine($"Current Grid: ({currentGrid.GridX}, {currentGrid.GridY})");
                results.AppendLine($"Grid Recent Change: {HasRecentlyChangedGrid(currentGrid)}");

                // Test AOI grids
                var aoiGrids = _aoiManager.GetAOIGrids(testPlayer.PosX, testPlayer.PosY, testPlayer.MapID);
                results.AppendLine($"AOI Grids Count: {aoiGrids.Count}");

                // Test visibility with nearby entities
                int playersInRange = 0;
                int npcsInRange = 0;
                int itemsInRange = 0;

                foreach (var grid in aoiGrids)
                {
                    foreach (var player in grid.Players)
                    {
                        if (player != testPlayer && IsEntityVisibleWithOverlap(testPlayer, player.PosX, player.PosY, grid, currentGrid))
                        {
                            playersInRange++;
                        }
                    }

                    foreach (var npc in grid.NPCs)
                    {
                        if (IsEntityVisibleWithOverlap(testPlayer, npc.Rxjh_X, npc.Rxjh_Y, grid, currentGrid))
                        {
                            npcsInRange++;
                        }
                    }

                    foreach (var item in grid.GroundItems)
                    {
                        if (IsEntityVisibleWithOverlap(testPlayer, item.PosX, item.PosY, grid, currentGrid))
                        {
                            itemsInRange++;
                        }
                    }
                }

                results.AppendLine($"Visible Entities: {playersInRange} players, {npcsInRange} NPCs, {itemsInRange} items");
                results.AppendLine("=== Test Completed Successfully ===");

                return results.ToString();
            }
            catch (Exception ex)
            {
                return $"Test failed with error: {ex.Message}";
            }
        }

        /// <summary>
        /// Calculate distance from a position to the nearest edge of a grid
        /// </summary>
        /// <param name="posX">Position X</param>
        /// <param name="posY">Position Y</param>
        /// <param name="grid">Target grid</param>
        /// <returns>Distance to nearest edge of the grid</returns>
        private static float GetDistanceToGrid(float posX, float posY, AOIGrid grid)
        {
            try
            {
                // Find closest point on grid boundary
                float closestX = Math.Max(grid.MinX, Math.Min(posX, grid.MaxX));
                float closestY = Math.Max(grid.MinY, Math.Min(posY, grid.MaxY));

                // Calculate distance to closest point
                float deltaX = posX - closestX;
                float deltaY = posY - closestY;

                return (float)Math.Sqrt(deltaX * deltaX + deltaY * deltaY);
            }
            catch
            {
                return float.MaxValue;
            }
        }

        #endregion
    }
}
