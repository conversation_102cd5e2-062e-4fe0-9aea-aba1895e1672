using System;
using System.Collections.Generic;

namespace RxjhServer.TestBot.Core
{
    /// <summary>
    /// Configuration cho Test Bot system
    /// </summary>
    public class BotConfig
    {
        #region Core Settings
        
        /// <summary>
        /// Bot có được bật không
        /// </summary>
        public bool IsEnabled { get; set; } = true;
        
        /// <summary>
        /// Tần suất update (milliseconds)
        /// </summary>
        public int UpdateInterval { get; set; } = 1000;
        
        /// <summary>
        /// Chế độ debug
        /// </summary>
        public bool DebugMode { get; set; } = true;
        
        #endregion
        
        #region Combat Settings
        
        /// <summary>
        /// Bật tính năng combat
        /// </summary>
        public bool CombatEnabled { get; set; } = true;
        
        /// <summary>
        /// Phạm vi tấn công (pixels)
        /// </summary>
        public int CombatRange { get; set; } = 100;
        
        /// <summary>
        /// Skill ID ưu tiên sử dụng
        /// </summary>
        public int PreferredSkillId { get; set; } = 0;
        
        /// <summary>
        /// Tự động tấn công PvP
        /// </summary>
        public bool PvPEnabled { get; set; } = false;
        
        #endregion
        
        #region Movement Settings
        
        /// <summary>
        /// Bật tính năng di chuyển
        /// </summary>
        public bool MovementEnabled { get; set; } = true;
        
        /// <summary>
        /// Tọa độ gốc X
        /// </summary>
        public float HomeX { get; set; } = 0;
        
        /// <summary>
        /// Tọa độ gốc Y
        /// </summary>
        public float HomeY { get; set; } = 0;
        
        /// <summary>
        /// Map ID gốc
        /// </summary>
        public int HomeMapId { get; set; } = 0;
        
        /// <summary>
        /// Phạm vi di chuyển tối đa từ vị trí gốc
        /// </summary>
        public int MaxRangeFromHome { get; set; } = 200;
        
        /// <summary>
        /// Thời gian anti-AFK (milliseconds)
        /// </summary>
        public int AntiAfkInterval { get; set; } = 3600000; // 1 hour
        
        #endregion
        
        #region Item Settings
        
        /// <summary>
        /// Bật tính năng nhặt đồ
        /// </summary>
        public bool ItemPickupEnabled { get; set; } = true;
        
        /// <summary>
        /// Phạm vi nhặt đồ
        /// </summary>
        public int ItemPickupRange { get; set; } = 50;
        
        /// <summary>
        /// Danh sách item ID được phép nhặt (empty = nhặt tất cả)
        /// </summary>
        public List<int> AllowedItemIds { get; set; } = new List<int>();
        
        /// <summary>
        /// Danh sách item ID bị cấm nhặt
        /// </summary>
        public List<int> BlockedItemIds { get; set; } = new List<int>();
        
        #endregion
        
        #region Status Settings
        
        /// <summary>
        /// Bật tự động hồi máu
        /// </summary>
        public bool AutoHealEnabled { get; set; } = true;
        
        /// <summary>
        /// Ngưỡng HP để hồi máu (%)
        /// </summary>
        public float HealThreshold { get; set; } = 0.7f;
        
        /// <summary>
        /// Bật tự động hồi mana
        /// </summary>
        public bool AutoManaEnabled { get; set; } = true;
        
        /// <summary>
        /// Ngưỡng MP để hồi mana (%)
        /// </summary>
        public float ManaThreshold { get; set; } = 0.2f;
        
        #endregion
        
        #region Buff Settings
        
        /// <summary>
        /// Bật tự động buff
        /// </summary>
        public bool AutoBuffEnabled { get; set; } = true;
        
        /// <summary>
        /// Thời gian giữa các lần buff (milliseconds)
        /// </summary>
        public int BuffInterval { get; set; } = 60000; // 1 minute
        
        /// <summary>
        /// Danh sách buff ID tự động sử dụng
        /// </summary>
        public List<int> AutoBuffIds { get; set; } = new List<int>();
        
        #endregion
        
        #region Extended Features
        
        /// <summary>
        /// Bật tính năng trading
        /// </summary>
        public bool TradingEnabled { get; set; } = false;
        
        /// <summary>
        /// Bật tính năng party
        /// </summary>
        public bool PartyEnabled { get; set; } = false;
        
        /// <summary>
        /// Bật tương tác NPC
        /// </summary>
        public bool NPCInteractionEnabled { get; set; } = false;
        
        /// <summary>
        /// Bật marketplace
        /// </summary>
        public bool MarketplaceEnabled { get; set; } = false;
        
        /// <summary>
        /// Bật cash shop
        /// </summary>
        public bool CashShopEnabled { get; set; } = false;
        
        /// <summary>
        /// Bật chat
        /// </summary>
        public bool ChatEnabled { get; set; } = false;
        
        #endregion
        
        #region Module Settings
        
        /// <summary>
        /// Settings tùy chỉnh cho từng module
        /// </summary>
        public Dictionary<string, Dictionary<string, object>> ModuleSettings { get; set; } = 
            new Dictionary<string, Dictionary<string, object>>();
        
        #endregion
        
        /// <summary>
        /// Tạo config mặc định từ player hiện tại
        /// </summary>
        /// <param name="player">Player instance</param>
        /// <returns>BotConfig với settings mặc định</returns>
        public static BotConfig CreateDefault(Players player)
        {
            return new BotConfig
            {
                HomeX = player.PosX,
                HomeY = player.PosY,
                HomeMapId = player.MapID,
                PreferredSkillId = player.OfflineTreoMaySkill_ID
            };
        }
        
        /// <summary>
        /// Lấy setting tùy chỉnh cho module
        /// </summary>
        /// <typeparam name="T">Kiểu dữ liệu</typeparam>
        /// <param name="moduleName">Tên module</param>
        /// <param name="settingName">Tên setting</param>
        /// <param name="defaultValue">Giá trị mặc định</param>
        /// <returns>Giá trị setting</returns>
        public T GetModuleSetting<T>(string moduleName, string settingName, T defaultValue = default(T))
        {
            if (ModuleSettings.TryGetValue(moduleName, out var moduleSettings) &&
                moduleSettings.TryGetValue(settingName, out var value))
            {
                try
                {
                    return (T)Convert.ChangeType(value, typeof(T));
                }
                catch
                {
                    return defaultValue;
                }
            }
            return defaultValue;
        }
        
        /// <summary>
        /// Đặt setting tùy chỉnh cho module
        /// </summary>
        /// <param name="moduleName">Tên module</param>
        /// <param name="settingName">Tên setting</param>
        /// <param name="value">Giá trị</param>
        public void SetModuleSetting(string moduleName, string settingName, object value)
        {
            if (!ModuleSettings.ContainsKey(moduleName))
            {
                ModuleSettings[moduleName] = new Dictionary<string, object>();
            }
            ModuleSettings[moduleName][settingName] = value;
        }
    }
}
